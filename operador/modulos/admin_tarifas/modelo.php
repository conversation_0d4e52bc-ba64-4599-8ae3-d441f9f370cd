<?php
require("../../config/mainModel.php");
class admin_tarifas
{
  /*
    METODOS DE LA BD.
    $this->BD->consultar($query); // Ejecuta la consulta y devuelve string.!
    $this->BD->devolver_array($query); // Ejecuta la consulta y devuelve array asociativo.!
    $this->BD->consultar("BEGIN"); // Antes de transacciones.!
    $this->BD->consultar("COMMIT"); // Commit para guardar datos.!
    $this->BD->consultar("ROLLBACK"); // Devolver datos si hay error.!
    $this->BD->numreg($query); // Devuelve el numero de registros de la consulta.!
  */

  private $BD, $id_user;

  public function __construct()
  {
    $BD = new BD();
    $this->BD = $BD;
    $this->BD->conectar();
    $oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
    $this->id_user = $oSession->VSid;
  }
  public function __destruct()
  {
    $this->BD->desconectar();
  }
  //Espacio Para declara las funciones que retornan los datos de la DB.

  function cargar_regional()
  {
    $regionales = $this->BD->Permisos_Dcs_Regionales();
    $c_tipo = "SELECT id,nombre FROM regional WHERE id in ($regionales);";
    return $this->BD->devolver_array($c_tipo);
  }

  function cargar_tarifas_regional($id_regional)
  {
    $condicion = "";
    $regionales = $this->BD->Permisos_Dcs_Regionales();
    if ($id_regional != "") {
      $condicion = " AND r.id IN ($id_regional) ";
    }
    $c_reg_tar = "SELECT 'Regional'as tipo,r.nombre,if(nv.estado_vigencia is null,'N/A',nv.estado_vigencia)AS estado_vigencia,if(nv.fecha_ini is null,'',nv.fecha_ini)AS fecha_ini,
                    if(nv.fecha_fin is null,'',nv.fecha_fin)AS fecha_fin,if(nv.valor is null,-1,nv.valor)AS valor,if(nv.id is null,0,nv.id)as id_nivel,if(nv.tarifas_menores is null,-1,nv.tarifas_menores)as tarifas_menores,r.id as id_regional
                    FROM regional r 
                    LEFT JOIN niveles_tarifas nv ON (r.id = nv.id_nivel AND nv.estado = 1 AND nv.tipo_nivel = 1)
                    WHERE r.id in ($regionales) $condicion ;";
    return $this->BD->devolver_array($c_reg_tar);
  }

  function guardar_tarifas($id_tarifa, $valor, $f_ini, $f_fin, $valor_menor, $id_regional)
  {
    $txtf_ini = "null";
    $txtf_fin = "null";
    $movimiento = 0;
    $error = 0;
    $id_nivel_audi = 0;
    if ($f_ini != "") {
      $txtf_ini = "'" . $f_ini . "'";
      $c_fechai_msq = "SELECT if('$f_ini' < curdate(),0,1)as fecha_correc";
      $r_fechai_msq = $this->BD->devolver_array($c_fechai_msq);
      if ($r_fechai_msq[0]["fecha_correc"] == 0) {
        return  array('estado' => 0, 'mns' => 'La fecha de vigencia debe ser mayor o igual a la fecha actual.');
        exit();
      }
    }
    if ($f_fin != "") {
      $txtf_fin = "'" . $f_fin . "'";
      $c_fecha_msq = "SELECT if('$f_fin' < curdate(),0,1)as fecha_correc";
      $r_fecha_msq = $this->BD->devolver_array($c_fecha_msq);
      if ($r_fecha_msq[0]["fecha_correc"] == 0) {
        return  array('estado' => 0, 'mns' => 'La fecha de expiración debe ser mayor o igual a la fecha actual.');
        exit();
      }
    }
    if (intval($id_tarifa) > 0) {
      $q_tarifa = "UPDATE niveles_tarifas
                      SET
                      valor = $valor,
                      tarifas_menores = $valor_menor,
                      fecha_ini = $txtf_ini,
                      fecha_fin = $txtf_fin,
                      estado = 1,
                      estado_vigencia = if($txtf_ini is null,1,if($txtf_ini>curdate(),0,1)) 
                      WHERE id = $id_tarifa AND tipo_nivel =1 AND id_nivel = $id_regional;";
      $movimiento = 2;
      $id_nivel_audi = $id_tarifa;
    } else {

      $q_tarifa = "INSERT INTO niveles_tarifas
                    (tipo_nivel,id_nivel,valor,estado,tarifas_menores,id_distribuidor,estado_vigencia,fecha_ini,fecha_fin)
                    VALUES
                    (1,$id_regional,$valor,1,$valor_menor,0,if($txtf_ini is null,1,if($txtf_ini>curdate(),0,1)),$txtf_ini,$txtf_fin);";
      $movimiento = 1;
    }
    $this->BD->consultar("BEGIN");

    if (!$this->BD->consultar($q_tarifa)) {
      $error = 1;
    }
    if (intval($id_tarifa) == 0) {
      $id_nivel_audi = $this->BD->last_insert_id();
    }

    $q_auditoria = "INSERT INTO audi_niveles_tarifas(id_nivel_tarifa, tipo_nivel, id_nivel, valor, estado, tarifas_menores, id_distribuidor, estado_vigencia, fecha_ini, fecha_fin, movimiento, fecha, hora, usuario) VALUES ($id_nivel_audi,1,$id_regional,$valor,1,$valor_menor,0,if($txtf_ini is null,1,if($txtf_ini>curdate(),0,1)),$txtf_ini,$txtf_fin,$movimiento,curdate(),curtime(),{$this->id_user})";

    if (!$this->BD->consultar($q_auditoria)) {
      $error = 1;
    }

    //ACTUALIZACION DE TARIFAS MENORES
    if (intval($valor_menor) == 0) {
      $error = $this->act_precio_menor($id_regional, $valor);
    }
    //ACTUALIZACION DE TARIFAS MENORES

    if ($error == 1) {
      $this->BD->consultar("ROLLBACK");
      return array("estado" => 0, "mns" => "Error al intentar asignar la tarifa");
    } else {
      $this->BD->consultar("COMMIT");
      return array("estado" => 1, "mns" => "Se ha creado asignado correctamente la tarifa.");
    }
  }

  function guardar_tarifa_gen($valor, $f_ini, $f_fin, $valor_menor)
  {
    $txtf_ini = "null";
    $txtf_fin = "null";
    $error = 0;
    $regionales = $this->BD->Permisos_Dcs_Regionales();

    if ($f_ini != "") {
      $txtf_ini = "'" . $f_ini . "'";
      $c_fechai_msq = "SELECT if('$f_ini' < curdate(),0,1)as fecha_correc";
      $r_fechai_msq = $this->BD->devolver_array($c_fechai_msq);
      if ($r_fechai_msq[0]["fecha_correc"] == 0) {
        return  array('estado' => 0, 'mns' => 'La fecha de vigencia debe ser mayor o igual a la fecha actual.');
        exit();
      }
    }
    if ($f_fin != "") {
      $txtf_fin = "'" . $f_fin . "'";
      $c_fecha_msq = "SELECT if('$f_fin' < curdate(),0,1)as fecha_correc";
      $r_fecha_msq = $this->BD->devolver_array($c_fecha_msq);
      if ($r_fecha_msq[0]["fecha_correc"] == 0) {
        return  array('estado' => 0, 'mns' => 'La fecha de expiración debe ser mayor o igual a la fecha actual.');
        exit();
      }
    }
    $this->BD->consultar("BEGIN");
    //ACTUALIZACION DE TARIFAS EXISTENTES
    $u_tarifa = "UPDATE niveles_tarifas
                      SET
                      valor = $valor,
                      tarifas_menores = $valor_menor,
                      fecha_ini = $txtf_ini,
                      fecha_fin = $txtf_fin,
                      estado = 1,
                      estado_vigencia = if($txtf_ini is null,1,if($txtf_ini>curdate(),0,1))
                      WHERE tipo_nivel =1 AND id_nivel in ($regionales);";

    if (!$this->BD->consultar($u_tarifa)) {
      $error = 1;
    }

    $i_auditoria = "INSERT INTO audi_niveles_tarifas(id_nivel_tarifa, tipo_nivel, id_nivel, valor, estado, tarifas_menores, id_distribuidor, estado_vigencia, fecha_ini, fecha_fin, movimiento, fecha, hora, usuario)(SELECT id, tipo_nivel, id_nivel, valor, estado, tarifas_menores, id_distribuidor, estado_vigencia, fecha_ini, fecha_fin,2,CURDATE(),CURTIME(),{$this->id_user} FROM niveles_tarifas WHERE tipo_nivel =1 AND id_nivel in ($regionales))";

    if (!$this->BD->consultar($i_auditoria)) {
      $error = 1;
    }

    //ACTUALIZACION DE TARIFAS EXISTENTES

    //INSERTAR TARIFAS QUE NO EXISTEN
    $i_tarifa = "INSERT INTO niveles_tarifas
                    (tipo_nivel,id_nivel,valor,estado,tarifas_menores,id_distribuidor,estado_vigencia,fecha_ini,fecha_fin)
                    (SELECT 1 AS tipo_nivel,id as id_nivel,$valor as valor,-4 as estado,$valor_menor as tarifas_menores,0 as id_distribuidor,if($txtf_ini is null,1,if($txtf_ini>curdate(),0,1)) as estado_vigencia,
                      $txtf_ini as fecha_ini,$txtf_fin as fecha_fin 
                     FROM regional WHERE id not in (SELECT id_nivel FROM niveles_tarifas WHERE tipo_nivel = 1));";
    if (!$this->BD->consultar($i_tarifa)) {
      $error = 1;
    }

    $i_auditoria_cre = "INSERT INTO audi_niveles_tarifas(id_nivel_tarifa, tipo_nivel, id_nivel, valor, estado, tarifas_menores, id_distribuidor, estado_vigencia, fecha_ini, fecha_fin, movimiento, fecha, hora, usuario)(SELECT id, tipo_nivel, id_nivel, valor, 1, tarifas_menores, id_distribuidor, estado_vigencia, fecha_ini, fecha_fin,1,CURDATE(),CURTIME(),{$this->id_user} FROM niveles_tarifas WHERE estado =-4)";

    if (!$this->BD->consultar($i_auditoria_cre)) {
      $error = 1;
    }

    $u_tarifa_cre = "UPDATE niveles_tarifas
                      SET
                      estado = 1
                      WHERE estado = -4;";

    if (!$this->BD->consultar($u_tarifa_cre)) {
      $error = 1;
    }

    //INSERTAR TARIFAS QUE NO EXISTEN FIN

    //ACTUALIZACION DE TARIFAS MENORES
    if (intval($valor_menor) == 0) {
      $error = $this->act_precio_menor($regionales, $valor);
    }
    //ACTUALIZACION DE TARIFAS MENORES

    if ($error == 1) {
      $this->BD->consultar("ROLLBACK");
      return array("estado" => 0, "mns" => "Error al intentar asignar la tarifa general.");
    } else {
      $this->BD->consultar("COMMIT");
      return array("estado" => 1, "mns" => "Se asigno correctamente la tarifa general.");
    }
  }

  function eliminar_tarifa($id_tarifa)
  {
    $error = 0;
    $this->BD->consultar("BEGIN");
    $u_tarifa = "UPDATE niveles_tarifas
                    SET
                    estado = 0
                    WHERE id =$id_tarifa;";
    if (!$this->BD->consultar($u_tarifa)) {
      $error = 1;
    }

    $i_auditoria = "INSERT INTO audi_niveles_tarifas(id_nivel_tarifa, tipo_nivel, id_nivel, valor, estado, tarifas_menores, id_distribuidor, estado_vigencia, fecha_ini, fecha_fin, movimiento, fecha, hora, usuario)(SELECT id, tipo_nivel, id_nivel, valor, estado, tarifas_menores, id_distribuidor, estado_vigencia, fecha_ini, fecha_fin,2,CURDATE(),CURTIME(),{$this->id_user} FROM niveles_tarifas WHERE id= $id_tarifa)";

    if (!$this->BD->consultar($i_auditoria)) {
      $error = 1;
    }

    if ($error == 1) {
      $this->BD->consultar("ROLLBACK");
      return array("estado" => 0, "mns" => "Error al intentar eliminar la tarifa.");
    } else {
      $this->BD->consultar("COMMIT");
      return array("estado" => 1, "mns" => "Se eliminado correctamente la tarifa.");
    }
  }

  public function act_precio_menor($regionales, $valor)
  { //0:no permite tarifas menores
    $error = 0;
    $u_niveles = "UPDATE niveles_tarifas
                        SET
                        valor = if(valor<$valor,$valor,valor)
                        WHERE id_regional in ($regionales) ;";

    if (!$this->BD->consultar($u_niveles)) {
      $error = 1;
    }

    return $error;
  }
} // Fin clase
