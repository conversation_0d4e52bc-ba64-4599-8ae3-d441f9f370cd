<?php
require_once "../../config/mainModel.php";

class val_permiso
{
  //Espacio Para declara las funciones que retornan los datos de la DB.
  public function Retornar_Permiso()
  {
    return array(
      'estado' => 4,
      "id_menu" => 0,
      "json_menu" => ""
    );
    if (isset($_SESSION[$GLOBALS["SESION_OP"]])) {
      $oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
      $id = $oSession->VSid;
      $perfil = $oSession->VSperfil;

      $BD = new BD();
      $BD->conectar();

      global $mods;

      $modulo = "?mod=" . $mods;

      if ($mods != "login" && $mods != "principal" && $mods != "changePass" && $mods != "recopass" && $mods != "val_perm") {
        if ($id != "1") {
          $sql = "SELECT menu.id from usuarios, {$GLOBALS["BD_NAME"]}.menu, menu_perfiles WHERE usuarios.id = " . $id . " AND usuarios.id_perfil = " . $perfil . " and usuarios.id_perfil = menu_perfiles.id_perfil and menu.id = menu_perfiles.id_menu and menu_perfiles.estado = 1 and menu.estado = 1 and url = '$modulo' limit 1";
        } else {
          $sql = "SELECT id from {$GLOBALS["BD_NAME"]}.menu WHERE url = '$modulo' and estado = 1 limit 1";
        }

        $res = $BD->consultar($sql);
        $existe = $BD->numreg($res);

        if ($existe == 0) {
          $sql = "SELECT id,url from menu WHERE url = '$modulo' LIMIT 1";
          $res = $BD->consultar($sql);
          if ($BD->numreg($res) > 0) {
            return array(
              'estado' => 0,
              "id_menu" => $res->fields["id"],
              "json_menu" => ""
            );
          } else {
            $sql = "SELECT id_menu from audi_menu WHERE url = '$modulo' LIMIT 1";
            $res = $BD->consultar($sql);
            if ($BD->numreg($res) > 0) {
              $query_menu = "SELECT menu.id,menu.idpadre,menu.nombre,menu.descripcion,menu.url,menu.orden,menu.target, if(menu.palabras_claves IS NULL,'',menu.palabras_claves) AS palabras_claves FROM menu WHERE id = {$res->fields["id_menu"]}";
              $array_menu = $BD->devolver_array($query_menu);

              return array(
                'estado' => 3,
                "id_menu" => $res->fields["id_menu"],
                "json_menu" => json_encode($array_menu, true)
              );
            }
          }

          return array(
            'estado' => 0,
            "id_menu" => 0,
            "json_menu" => ""
          );
        } else {
          return array(
            'estado' => 1,
            "id_menu" => 0,
            "json_menu" => ""
          );
        }
      } else {
        return array(
          'estado' => 1,
          "id_menu" => 0,
          "json_menu" => ""
        );
      }
    } else {
      return array(
        'estado' => 2,
        "id_menu" => 0,
        "json_menu" => ""
      );
    }

    $BD->desconectar();
  }

  public function consultar_noti()
  {
    return array();
    $BD = new BD();
    $BD->conectar();

    if (isset($_SESSION[$GLOBALS["SESION_OP"]])) {
      $oSession = unserialize($_SESSION[$GLOBALS["SESION_OP"]]);
      $id_user = $oSession->VSid;
      $id_perfil = $oSession->VSperfil;

      $permReg = $BD->Permisos_Dcs_Regionales();
      $permDist = $BD->Permisos_Dcs_Distribuidores();
      $permTerri = $BD->Permisos_Dcs_Territorios();
      $permZonas = $BD->Permisos_Dcs_Zonas();

      if (strlen($permReg) > 0)
        $permReg = $permReg;
      else
        $permReg = 0;
      if (strlen($permDist) > 0)
        $permDist = $permDist;
      else
        $permDist = 0;
      if (strlen($permTerri) > 0)
        $permTerri = $permTerri;
      else
        $permTerri = 0;
      if (strlen($permZonas) > 0)
        $permZonas = $permZonas;
      else
        $permZonas = 0;

      $sql = "SELECT COUNT(DISTINCT ndn.id) as cant_noticias
	                FROM {$GLOBALS["BD_NAME"]}.noticias_promociones AS ndn 
	                INNER JOIN {$GLOBALS["BD_NAME"]}.niveles_dcs_noticias AS np ON (np.id_noticia = ndn.id)
	                WHERE np.nivel=1 AND ndn.estado=1 AND ndn.vigente=1 
	                  AND ((np.tipo=1 and np.id_tipo IN ($permReg))
	                  OR(np.tipo=2 and np.id_tipo IN ($permDist))
	                  OR(np.tipo=3 and np.id_tipo IN ($permTerri))
	                  OR(np.tipo=4 and np.id_tipo IN ($permZonas))) 
                    AND ndn.id NOT IN (SELECT nl.id_noticia FROM {$GLOBALS["BD_NAME"]}.noticias_leidas nl WHERE nl.id_usuario=$id_user AND nl.id_distri = 0 AND (nl.leido = 1 AND ndn.estado_mo = 0 || ndn.estado_mo = 1 AND nl.leido_mo = 1) GROUP BY id_noticia)";

      $res_noti = $BD->devolver_array($sql);

      $sql = "SELECT COUNT(DISTINCT enc.id) as cant_encuesta
	                FROM {$GLOBALS["BD_NAME"]}.enc__encuesta as enc
	                INNER JOIN {$GLOBALS["BD_NAME"]}.enc__niveles_dcs AS dcs ON (dcs.id_encuesta=enc.id)
	                WHERE enc.estado=1 AND enc.vigente=1 AND dcs.nivel=1
	                AND ((dcs.tipo=1 and dcs.id_tipo IN ($permReg))
	                    OR(dcs.tipo=2 and dcs.id_tipo IN ($permDist))
	                    OR(dcs.tipo=3 and dcs.id_tipo IN ($permTerri))
	                    OR(dcs.tipo=4 and dcs.id_tipo IN ($permZonas)))
	                    AND enc.id NOT IN (SELECT id_encuesta FROM enc__respuestas_encuesta WHERE id_usuario = $id_user AND distri = 0 AND id_encuesta = enc.id GROUP BY id_encuesta)";

      $res_enc = $BD->devolver_array($sql);

      return array(
        "cant_enc" => $res_enc[0]["cant_encuesta"],
        "cant_noti" => $res_noti[0]["cant_noticias"]
      );
    } else {
      return array();
    }
  }
}
