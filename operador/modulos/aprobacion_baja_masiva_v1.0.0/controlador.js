const URL_MODULO = 'modulos/aprobacion_baja_masiva_v1.0.0/';
$(document).ready(function () {
	$('.fecha').datepicker({
		format: 'yyyy-mm-dd',
		todayHighlight: true,
		todayBtn: true
	});
	cargar_distrib();

	$("#btn_buscar").click(function (e) {
		cargar_solicitud_baja();
	});
});
var dialogRef;


function cargar_distrib() {
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'cargar_distrib'
		})
	}).then(response => response.json()).then(data => {
		var html_select = "";
		html_select += '<option value = "">Seleccionar...</option>';
		$.each(data, function (index, row) {
			html_select += '<option value = "' + row.id + '">' + row.nombre + '</option>';
		});
		$("#distri").html(html_select);
	}).catch(error => {
		console.error('Error:', error);
	});
}


function cargar_solicitud_baja() {
	var f_ini = $("#f_ini").val();
	var f_fin = $("#f_fin").val();
	var distri = $("#distri").val();

	if (f_ini == "" || f_fin == "") {
		Notificacion("La fecha inicial y la fecha final son obligatorias.", "error");
		return false;
	}

	if (f_ini > f_fin) {
		Notificacion("La fecha inicial debe ser menor a la fecha final.", "error");
		return false;
	}

	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'cargar_solicitud_baja',
			f_ini: f_ini,
			f_fin: f_fin,
			distri: distri
		})
	}).then(response => response.json()).then(data => {
		crear_dtable(data);
	}).catch(error => {
		console.error('Error:', error);
	});
}

function cargar_solicitud_detalle(id_solicitud) {
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'cargar_solicitud_detalle',
			id_solicitud: id_solicitud
		})
	}).then(response => response.json()).then(data => {
		crear_dtable_detalle(data);
	}).catch(error => {
		console.error('Error:', error);
	});
}
var dtable;
var dtable2;
function crear_dtable(datos) {

	if ($.fn.DataTable.isDataTable('#t_reporte')) {
		dtable.clear();
		dtable.destroy();
	}
	var columnas = [
		{ "data": "fecha" },
		{ "data": "hora" },
		{ "data": "distribuidor" },
		{ "data": "nombre_usuario" },
		{ "data": "nombre_archivo" },
		{ "data": "cantidad" },
		{ "data": "cantidad_aceptada" },
		{ "data": "id" },
		{ "data": "id" }
	];

	columnas_defect = [{
		"targets": 5,
		"data": "cantidad",
		render: function (data, type, row) {
			return format(data, 0);
		}
	}, {
		"targets": 6,
		"data": "cantidad_aceptada",
		render: function (data, type, row) {
			return format(data, 0);
		}
	}, {
		"targets": 7,
		"data": "id",
		render: function (data, type, row) {
			return "<i style='cursor:pointer; font-size:19px;' class='glyphicon glyphicon-zoom-in detalle text-primary'></i>";
		}
	}, {
		"targets": 8,
		"data": "id",
		render: function (data, type, row) {
			return "<i style='cursor:pointer; font-size:19px;' class='glyphicon glyphicon-ok-sign aprob text-success'></i>&nbsp;&nbsp;&nbsp;&nbsp;" +
				"<i style='cursor:pointer; font-size:19px;' class='glyphicon glyphicon-remove-sign recha text-danger'></i>";
		}
	}];
	//return "<i style='cursor:pointer; font-size:19px;' class='glyphicon glyphicon-zoom-in det_vis text-primary'></i>";
	funtion_table = function () {
		$(".detalle").unbind("click");
		$(".detalle").click(function () {
			$(".btn_mod_item").html('<button type="button" class="btn btn-default" data-dismiss="modal">Cancelar</button>');
			$(".simcard_baja").hide();
			var data = dtable.row($(this).parents('tr')).data();
			cargar_solicitud_detalle(data.id);
		});

		$(".aprob").unbind("click");
		$(".aprob").click(function () {
			var data = dtable.row($(this).parents('tr')).data();
			$(".btn_mod_item").html('<button type="button" class="btn btn-primary" id="btn_aceptar_item" data-dismiss="modal">Aceptar</button> <button type="button" class="btn btn-danger" data-dismiss="modal">Cancelar</button>');
			$("#btn_aceptar_item").unbind("click");
			$("#btn_aceptar_item").click(function (e) {
				dialogRef = BootstrapDialog.confirm("¿Seguro desea aprobar esta solicitud?", function (result) {
					if (result) {
						acep_canc_solicitud(1, data.id, '');
					}
				});
			});
			$(".simcard_baja").html("<h2>Desea dar de baja las siguientes simcards:</h2><br><h4>Simcards Solicitadas: " + data.cantidad + "<br>Simcards Aceptadas: " + data.cantidad_aceptada + "</h4>");
			$(".simcard_baja").show();

			cargar_solicitud_detalle(data.id);
		});
		$(".recha").unbind("click");
		$(".recha").click(function () {
			var data = dtable.row($(this).parents('tr')).data();
			dialogRef = BootstrapDialog.show({
				title: 'Comentario de rechazo',
				message: $('<textarea class="form-control" id = "txt_observacion" placeholder = "Observación..." ></textarea>'),
				buttons: [{
					label: 'Aceptar',
					cssClass: 'btn-primary',
					action: function () {
						var txt_ob = $("#txt_observacion").val();
						if (txt_ob != "") {
							acep_canc_solicitud(2, data.id, txt_ob);
						} else {
							Notificacion("El campo de Observacion es obligatorio", "warning");
							return false;
						}
					}
				}, {
					label: 'Cancelar',
					cssClass: 'btn-danger',
					action: function () {
						dialogRef.close();
					}
				}]
			});
		});
	};
	var cabecera = "<thead><tr><th>Fecha</th><th>Hora</th><th>Distribuidor</th><th>Usuario</th><th>Archivo</th><th>Cantidad</th><th>Cantidad Aceptada</th><th class='all'>Detalle</th><th class='all'>Aprobar/Rechazar</th></tr></thead>";
	$("#t_reporte").html(cabecera);
	dtable = $('#t_reporte').DataTable({
		data: datos,
		bFilter: false,
		columns: columnas,
		columnDefs: columnas_defect,
		fnDrawCallback: funtion_table
	});
	$("#consulta").show();


}

function crear_dtable_detalle(datos) {

	if ($.fn.DataTable.isDataTable('#t_sim_cargadas')) {
		dtable2.clear();
		dtable2.destroy();
	}
	var columnas = [
		{ "data": "idpdv" },
		{ "data": "iccid" }
	];

	columnas_defect = [];
	//return "<i style='cursor:pointer; font-size:19px;' class='glyphicon glyphicon-zoom-in det_vis text-primary'></i>";
	funtion_table = function () { };
	var cabecera = "<thead><tr><th>IDPDV</th><th>ICCID</th></tr></thead>";
	$("#t_sim_cargadas").html(cabecera);
	dtable2 = $('#t_sim_cargadas').DataTable({
		data: datos,
		bFilter: false,
		columns: columnas,
		columnDefs: columnas_defect,
		fnDrawCallback: funtion_table
	});
	$("#mod_det_sim").modal("show");
	//$("#mod_det_sim").modal({keyboard: false});


}


function acep_canc_solicitud(tipo, id_archivo, observacion) {
	fetch(`${URL_MODULO}/controlador.php`, {
		method: 'POST',
		headers: {
			'Content-Type': 'application/json'
		},
		body: JSON.stringify({
			accion: 'acep_canc_solicitud',
			tipo: tipo,
			id_archivo: id_archivo,
			observacion: observacion
		})
	}).then(response => response.json()).then(data => {
		if (data.estado == 1) {
			dialogRef.close();
			Notificacion(data.msg, "success");
			cargar_solicitud_baja();
		} else {
			Notificacion(data.msg, "error");
		}
	}).catch(error => {
		console.error('Error:', error);
	});
}




