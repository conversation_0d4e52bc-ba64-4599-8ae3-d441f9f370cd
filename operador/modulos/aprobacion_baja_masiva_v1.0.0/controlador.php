<?php
include_once("../../config/mainController.php"); // Incluye el Controlador Principal
include_once("./modelo.php");	// Incluye el Modelo.

$controller = new mainController(); // Instancia a la clase MainController
$modelo = new aprobacion_baja_masiva(); // Instancia a la clase del modelo
try // Try, manejo de Errores
{
	$metodo = $_SERVER['REQUEST_METHOD'];
	$tipo_res = "";
	$response = null;
	// Se manejaran dos tipos JSON y HTML
	// Dependiendo del método de la petición ejecutaremos la acción correspondiente.
	// Por ahora solo POST, todas las llamadas se haran por POST

	$variables = array();

	if (!empty($_POST)) {
		$variables = $_POST;
	} else if ($metodo == 'POST' && $_SERVER['CONTENT_TYPE'] == 'application/json') {
		// leer json 
		$json = file_get_contents('php://input');
		$variables = json_decode($json, true);
	}

	// Evita que ocurra un error si no manda accion.
	if (!isset($variables['accion'])) {
		http_response_code(404);
		header("Content-type: text/plain; charset=utf-8");
		echo "0";
		return;
	}

	$accion = $variables['accion'];

	// Dependiendo de la accion se ejecutaran las tareas y se definira el tipo de respuesta.
	switch ($accion) {
		case 'cargar_distrib':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$response = $modelo->cargar_distrib();
			break;
		case 'cargar_solicitud_baja':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$f_ini = $variables["f_ini"];
			$f_fin = $variables["f_fin"];
			$distri = $variables["distri"];
			$response = $modelo->cargar_solicitud_baja($f_ini, $f_fin, $distri);
			break;
		case 'cargar_solicitud_detalle':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$id_solicitud = $variables["id_solicitud"];
			$response = $modelo->cargar_solicitud_detalle($id_solicitud);
			break;
		case 'acep_canc_solicitud':
			$tipo_res = 'JSON'; //Definir tipo de respuesta;
			$tipo = $variables["tipo"];
			$id_archivo = $variables["id_archivo"];
			$observacion = $variables["observacion"];
			$response = $modelo->acep_canc_solicitud($tipo, $id_archivo, $observacion);
			break;
		default:
			http_response_code(404);
			header("Content-type: text/plain; charset=utf-8");
			echo "0";
			return;
	}

	// Respuestas del Controlador
	if ($tipo_res == "JSON") {
		header("Content-type: application/json; charset=utf-8");
		$json_output = json_encode($response, true); // $response será un array con los datos de nuestra respuesta.
		if (json_last_error() !== JSON_ERROR_NONE) {
			// Si hubo un error en la codificación JSON
			throw new Exception("Error al codificar la respuesta JSON: " . json_last_error_msg());
		} else {
			echo $json_output;
		}
	} elseif ($tipo_res == "HTML") {
		header("Content-type: text/html; charset=utf-8");
		echo $response; // $response será un html con el string de nuestra respuesta.
	} else {
		header("Content-type: text/plain; charset=utf-8");
		echo $response; // $response será un texto plano con el string de nuestra respuesta.
	}
} catch (Exception $e) {
	header("Content-type: application/json; charset=utf-8");
	http_response_code(500);
	echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
} catch (Error $e) {
	header("Content-type: application/json; charset=utf-8");
	http_response_code(500);
	echo json_encode(array("error" => true, "mensaje" => $e->getMessage()));
}
