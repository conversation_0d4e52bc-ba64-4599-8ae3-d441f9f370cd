<?php
require_once("../../config/mainModel.php");
class aprobacion_baja_masiva
{
  /*
    METODOS DE LA BD.
    $this->BD->consultar($query); // Ejecuta la consulta y devuelve string.!
    $this->BD->devolver_array($query); // Ejecuta la consulta y devuelve array asociativo.!
    $this->BD->consultar("BEGIN"); // Antes de transacciones.!
    $this->BD->consultar("COMMIT"); // Commit para guardar datos.!
    $this->BD->consultar("ROLLBACK"); // Devolver datos si hay error.!
    $this->BD->numreg($query); // Devuelve el numero de registros de la consulta.!
  */
  private $BD;

  public function __construct()
  {
    $BD = new BD();
    $this->BD = $BD;
    $this->BD->conectar();
  }
  public function __destruct()
  {
    $this->BD->desconectar();
  }
  //Espacio Para declara las funciones que retornan los datos de la DB.

  public function cargar_distrib()
  {
    $permisos_distri = $this->BD->Permisos_Dcs_Distribuidores();
    $c_distri = "SELECT id,nombre FROM {$GLOBALS["BD_NAME"]}.distribuidores WHERE id in ($permisos_distri);";
    return $this->BD->devolver_array($c_distri);
  }

  public function cargar_solicitud_baja($f_ini, $f_fin, $distri)
  {
    $permisos_distri = $this->BD->Permisos_Dcs_Distribuidores();
    $condicion = "";

    if ($distri != "") {
      $condicion .= " AND b.id_distri = $distri ";
    }


    $c_solicitud = "SELECT b.fecha,b.hora,d.nombre as distribuidor,ud.nombre_usuario,b.nombre_archivo,b.cantidad,b.cantidad_aceptada,b.id
                    FROM {$GLOBALS["BD_NAME"]}.baja__archivo b
                    INNER JOIN {$GLOBALS["BD_NAME"]}.distribuidores d ON (b.id_distri = d.id)
                    LEFT JOIN {$GLOBALS["BD_NAME"]}.usuarios_distri ud ON (ud.id_distri = b.id_distri AND ud.id_usuario = b.id_usuario)
                    WHERE b.fecha >= '$f_ini' AND b.fecha <= '$f_fin' AND b.id_distri IN ($permisos_distri) AND b.estado_solicitud = 0 $condicion ;";

    //echo $c_solicitud;exit;
    return $this->BD->devolver_array($c_solicitud);
  }

  public function cargar_solicitud_detalle($id_solicitud)
  {
    $c_item = "SELECT iccid,idpdv 
                FROM {$GLOBALS["BD_NAME"]}.baja__archivo_items
                WHERE id_archivo = $id_solicitud AND estado = 1;";
    return $this->BD->devolver_array($c_item);
  }

  public function acep_canc_solicitud($tipo, $id_archivo, $observacion)
  {
    $error = 0;
    $c_estado_sol = "SELECT id FROM {$GLOBALS["BD_NAME"]}.baja__archivo WHERE id = $id_archivo AND estado_solicitud = 0;";
    $r_estado_sol = $this->BD->devolver_array($c_estado_sol);
    if (count($r_estado_sol) <= 0) {
      return array("estado" => -1, "msg" => 'Error al cancelar la solicitud, la solicitud ya se encuentra en otro estado.');
      exit();
    }
    if ($tipo == 1) { //ACEPTACION
      $this->BD->consultar("BEGIN");

      $u_arch_item = " UPDATE {$GLOBALS["BD_NAME"]}.baja__archivo SET estado_solicitud = 1,fecha_baja = CURDATE(), hora_baja = CURTIME() WHERE id = $id_archivo; ";
      if (!$this->BD->consultar($u_arch_item)) {
        $error = 1;
      }

      $u_sim = "UPDATE {$GLOBALS["BD_NAME"]}.simcards s
                  INNER JOIN {$GLOBALS["BD_NAME"]}.baja__archivo_items bi ON (bi.id_simcard = s.id)
                  SET
                  s.activo = 2,
                  s.fecha_acmanual = curdate(),
                  s.hora_acmanual = curtime()
                  WHERE s.activo = 0 AND bi.id_archivo = $id_archivo AND bi.estado = 1;";

      if (!$this->BD->consultar($u_sim)) {
        $error = 1;
      }

      $u_inv_pos = "UPDATE {$GLOBALS["BD_NAME"]}.inventario__punto AS ip
                            INNER JOIN {$GLOBALS["BD_NAME"]}.baja__archivo_items bi ON (bi.iccid = ip.serial)
                            SET
                            ip.activo = 2
                            WHERE ip.tipo = 1 AND ip.activo = 0 AND bi.id_archivo = $id_archivo AND bi.estado = 1;";

      if (!$this->BD->consultar($u_inv_pos)) {
        $error = 1;
      }

      $c_group_concat = "SET @@group_concat_max_len = 1000000";
      $this->BD->consultar($c_group_concat);

      $c_puntos = "SELECT group_concat(idpdv)as puntos FROM {$GLOBALS["BD_NAME"]}.baja__archivo_items WHERE id_archivo= $id_archivo AND estado = 1;";
      $r_puntos = $this->BD->devolver_array($c_puntos);
      $zonas = "0";
      if (count($r_puntos) > 0) {
        $c_group_concat = "SET @@group_concat_max_len = 1000000";
        $this->BD->consultar($c_group_concat);
        $c_zon = "SELECT group_concat(zona)as zonas FROM {$GLOBALS["BD_POS"]}.puntos WHERE idpos in (" . $r_puntos[0]["puntos"] . ");";
        $r_zon = $this->BD->devolver_array($c_zon);
        $zonas = $r_zon[0]["zonas"];
        $d_stock_pos = "DELETE FROM {$GLOBALS["BD_NAME"]}.stock_puntos WHERE id_pos in (" . $r_puntos[0]["puntos"] . ");";
        if (!$this->BD->consultar($d_stock_pos)) {
          $error = 1;
        }

        $c_stock_pos = "INSERT INTO
                            {$GLOBALS["BD_NAME"]}.stock_puntos (
                              id_pos,
                              id_refe,
                              tipo,
                              stock)
                              (SELECT id_pos,id_referencia,1 as tipo,count(id) as cant FROM simcards 
                              WHERE activo = 0 AND id_pos IN ({$r_puntos[0]["puntos"]}) AND estado = 1 AND id_combo = 0 
                              GROUP BY id_pos,id_referencia)";
        if (!$this->BD->consultar($c_stock_pos)) {
          $error = 1;
        }
      }


      if ($error == 1) {
        $this->BD->consultar("ROLLBACK");
        $response = array("estado" => -1, "msg" => 'Error al aceptar la solicitud.');
      } elseif ($error == 0) {
        $this->BD->consultar("COMMIT");
        $this->notificar_usuarios($zonas);
        $response = array("estado" => 1, "msg" => 'Se ha aceptado la solicitud con exito.');
      }
    } else if ($tipo == 2) { //RECHAZO
      $u_arch_item = " UPDATE {$GLOBALS["BD_NAME"]}.baja__archivo SET estado_solicitud = 2,observacion = '" . trim($observacion) . "',
                          fecha_baja = CURDATE(), hora_baja = CURTIME() 
                          WHERE id = $id_archivo; ";
      if (!$this->BD->consultar($u_arch_item)) {
        $response = array("estado" => -1, "msg" => 'Error al cancelar la solicitud.');
      } else {
        $response = array("estado" => 1, "msg" => 'Se ha cancelado la solicitud.');
      }
    }
    return $response;
  }


  public function notificar_usuarios($zonas)
  {
    $sql = "SELECT id_usus FROM niveles_dcs WHERE (tipo = 4 AND id_tipo in ($zonas)) OR (p1 in ($zonas) AND tipo = 5) OR (tipo = 3 AND id_tipo in (SELECT territorio FROM {$GLOBALS["BD_NAME"]}.zonas WHERE id in ($zonas)))";
    $red = $this->BD->consultar($sql);
    if ($this->BD->numreg($red) > 0) {
      while (!$red->EOF) {
        $id_ususrio = $red->fields["id_usus"];
        $sql_u = "SELECT idNotif AS token_notif FROM usuarios WHERE id = $id_ususrio AND idNotif IS NOT NULL";
        $redtok = $this->BD->consultar($sql_u);

        if ($this->BD->numreg($redtok) > 0) {
          $this->BD->notificar($redtok->fields["token_notif"], "", "", 110);
        }

        $red->MoveNext();
      }
    }
  }
} // Fin clase
