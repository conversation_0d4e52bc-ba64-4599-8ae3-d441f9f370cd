var Menu = "";
var url_refes = "static/img/catalogo_referencias";
var loader_dw;
var Url_reportes = "";

var eso;
var mensaje;
var get;
var ladda;
var element_ladda;
var padres_hijos = [];
const _loading = {};
var loader = false;

$(document).ready(() => {
  get = get_vars();
  element_ladda =
    document.querySelector("button[type=submit]") ||
    document.querySelector("button[type=button]");

  $(".close").click(() => {
    $(".modal-backdrop").hide();
  });

  $(".modal, .fade, .in").click(() => {
    $(".modal-backdrop:eq( 1 )").removeClass("fade");
    $(".modal-backdrop:eq( 1 )").removeClass("in");
    $(".modal-backdrop:eq( 1 )").hide();
  });

  if (
    get.mod != "login" &&
    get.mod != "principal" &&
    get.mod != "changePass" &&
    get.mod != "recopass" &&
    get.mod != "val_perm" &&
    get.mod != "cambio_pass" &&
    get.mod != "crear_mod" &&
    get.mod != "modulo_prueba" &&
    get.mod != "crear_preguntas" &&
    get.mod != "crear_respuestas" &&
    get.mod != "responder_encuesta" &&
    get.mod != "admin_encuesta" &&
    get.mod != "gestor_operador_menumbx" &&
    get.mod != "gestor_distri_menumbx"
  ) {
    fetch("modulos/val_permiso/controlador.php", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify({ accion: "val_perm", mod: get.mod })
    }).then((datos) => {
      let datosLocal2 = {};
      if (element_ladda) ladda = Ladda.create(element_ladda);
      if (datos.estado == 0) {
        BootstrapDialog.confirm(
          "No cuenta con permisos para acceder a este modulo.",
          (result) => {
            if (result) location.href = "?mod=principal";
          }
        );
        setTimeout(() => {
          location.href = "?mod=principal";
        }, 6000);
      } else if (datos.estado == 2) {
        BootstrapDialog.confirm(
          "Lo sentimos pero tu sesión se encuentra cerrada.",
          (result) => {
            if (result) window.location = "?cerrar=S";
          }
        );
      } else if (datos.estado == 3) {
        let datosLocal = JSON.parse(localStorage.getItem("m_a_ur"));
        let menuActual = datosLocal.MENU;
        let estadoEncuesta = datosLocal.est_enc;
        let menuCambio = JSON.parse(datos.json_menu);

        $.each(menuActual, (indice, fila) => {
          if (parseInt(fila.id_menu) == parseInt(datos.id_menu)) {
            menuActual[indice].id_menu = datos.id_menu;
            menuActual[indice].idpadre = menuCambio[0].idpadre;
            menuActual[indice].nombre = menuCambio[0].nombre;
            menuActual[indice].descripcion = menuCambio[0].descripcion;
            menuActual[indice].url = menuCambio[0].url;
            menuActual[indice].target = menuCambio[0].target;
            menuActual[indice].palabras_claves =
              menuCambio[0].palabras_claves;
            menuActual[indice].desplegar = 0;
          }
        });

        datosLocal2.MENU = menuActual;
        datosLocal2.est_enc = estadoEncuesta;
        localStorage.m_a_ur = JSON.stringify(datosLocal2);
        location.href = menuCambio[0].url;
      }
    }).catch((error) => {
      console.error(error);
    });
  }

  if (
    localStorage.getItem("est_enc") > 0 &&
    get.mod != "responder_encuesta" &&
    get.mod != "login" &&
    get.mod != "admin_encuesta"
  ) {
    window.location = "?mod=responder_encuesta";
  }

  if (get.mod != "login") {
    let Datos_Menu = JSON.parse(localStorage.getItem("m_a_ur"));
    let MENU = Datos_Menu.MENU;
    CargarMenu(0, MENU);
    $("#CargarMenu").append(Menu);
  }

  if (
    get.mod != "login" &&
    get.mod != "principal" &&
    get.mod != "changePass" &&
    get.mod != "recopass" &&
    get.mod != "val_perm" &&
    get.mod != "cambio_pass" &&
    get.mod != "crear_mod" &&
    get.mod != "modulo_prueba" &&
    get.mod != "crear_preguntas" &&
    get.mod != "crear_respuestas" &&
    get.mod != "responder_encuesta" &&
    get.mod != "admin_encuesta"
  ) {
    if (
      get.mod !== "gestor_operador_menumbx" &&
      get.mod !== "gestor_distri_menumbx"
    )
      Validar_Permiso(get.mod);

    $("select").css("width", "100%").addClass("select2").select2({});
    $("select").css({ width: "10%", position: "absolute", display: "" });
  }

  $("input[is-data]").change(function () {
    $(this).popover("destroy");
    let boolean = !0;

    if ($(this).val() != "") {
      let opciones = $(this).attr("is-data").split(",");

      if (
        validar_datox(opciones[0], opciones[1], opciones[2], $(this).val()) == !1
      ) {
        let especiales = "";

        if (opciones[2] == 1) {
          especiales = ", ademas no debe contener caracteres especiales";
        }

        if (opciones[2] == 4) {
          especiales = ", ademas solo se permite letras y números";
        }

        let nombre_campo = $(this).attr("data-name");
        let d_error = $(this).parent().find(".error");

        if (!$.contains(window.document, d_error[0])) {
          $(this).parent().addClass("has-error");
          $(this).attr("title", "Campo no valido");
          $(this).attr("data-toggle", "popover");
          $(this).attr("data-placement", "top");
          $(this).attr(
            "data-content",
            "Debe tener un minimo de " +
            opciones[0] +
            " caracteres y maximo " +
            opciones[1] +
            especiales
          );
          $(this).popover("show");
        }
      } else {
        $(this).parent().removeClass("has-error");
      }
    }

    $("input[is-data]").each(function () {
      if ($(this).val() != "") {
        $(this).val($.trim($(this).val()));

        let opciones = $(this).attr("is-data").split(",");

        if (
          validar_datox(opciones[0], opciones[1], opciones[2], $(this).val()) == !1
        ) {
          let especiales = "";

          if (opciones[2] == 1) {
            especiales = ", ademas no debe contener caracteres especiales";
          }

          if (opciones[2] == 4) {
            especiales = ", ademas solo se permite letras y números";
          }

          let d_error = $("body").find(".appriseOverlay");

          if (!$.contains(window.document, d_error[0])) {
            $(this).parent().addClass("has-error");
            $(this).attr("title", "Campo no valido");
            $(this).attr("data-toggle", "popover");
            $(this).attr("data-placement", "top");
            $(this).attr(
              "data-content",
              "Debe tener un minimo de " +
              opciones[0] +
              " caracteres y maximo " +
              opciones[1] +
              especiales
            );
            $(this).popover("show");

            boolean = !1;
          }
        } else {
          $(this).parent().removeClass("has-error");
        }
      }
    });

    if (boolean == !1) {
      if ($(".btn_vl_cm_css").get(0).type == "submit") {
        localStorage.setItem("s_b_m_t", "s_y_e");

        $(".btn_vl_cm_css").attr("disabled", !0);
        $(".btn_vl_cm_css").get(0).type = "button";
        $(".btn_vl_cm_css").unbind("click");
        $(".btn_vl_cm_css").click(() => {
          Notificacion(
            "Este formulario ha sido modificado por medio de JavaScript",
            "error"
          );
        });
      } else {
        localStorage.setItem("i_d_bton", $(".btn_vl_cm_css").attr("id"));

        $(".btn_vl_cm_css").removeAttr("id");
        $(".btn_vl_cm_css").attr("disabled", !0);
        $(".btn_vl_cm_css").unbind("dblclick");
        $(".btn_vl_cm_css").dblclick(() => {
          Notificacion(
            "Este formulario ha sido modificado por medio de JavaScript",
            "error"
          );
        });
      }
    } else {
      if (localStorage.getItem("s_b_m_t") == "s_y_e") {
        $(".btn_vl_cm_css").attr("disabled", false);
        $(".btn_vl_cm_css").get(0).type = "submit";
        $(".btn_vl_cm_css").unbind("click");

        localStorage.removeItem("s_b_m_t");
      } else {
        if (localStorage.getItem("i_d_bton") != null) {
          $(".btn_vl_cm_css").attr("id", localStorage.getItem("i_d_bton"));
          $("#" + localStorage.getItem("i_d_bton")).bind("click");
          $(".btn_vl_cm_css").attr("disabled", false);
          $(".btn_vl_cm_css").unbind("dblclick");

          localStorage.removeItem("i_d_bton");
        }
      }
    }
  });
  $(".modal").on("hidden.bs.modal", function (e) {
    let form = $(this).find("form").attr("id");

    $("#" + form + "")
      .find("div.form-group")
      .each(function (index, el) {
        $(this).removeClass("has-error");
        $(this)
          .find("div.popover")
          .each(function (index, el) {
            $(this).remove();
          });
      });
    $("form input[is-data]").each(function (index, element) {
      if (!$(this).hasClass("no_reload")) {
        $(this).val("");
      }
      $("input[is-data]").each(function () {
        if ($(this).val() != "") {
        }
      });
    });
    $("form select.select2").each(function (index, element) {
      if (!$(this).hasClass("no_reload")) {
        $(this).val("");
      }
    });
  });
  $(".modal").on("shown.bs.modal", function (e) {
    $(".btn-primary").removeAttr("disabled", "disabled");
    $(".btn-primary").click(function () {
      let boton = $(this);
      $(".modal form").submit(function (event) { });
    });
  });
  if (get.mod != "login") {
    notificaciones();
    getUrl();
  }

  loadHtml("#cargar_visita_detalle", "template/detalle_visita/template.html");

  $("#buscarMenu").keyup(function (e) {
    /*if(e.keyCode == 13)
        {*/
    var palabra = $(this).val().toLowerCase();
    if (palabra != "") {
      let datosLocal2 = {};
      let datosLocal = JSON.parse(localStorage.getItem("m_a_ur"));
      let estadoEncuesta = datosLocal.est_enc;
      menuActual = filtrar_menu(palabra);
      Menu = "";
      CargarMenu(0, menuActual);
      $("#CargarMenu").html("");
      $("#CargarMenu").append(Menu);
    } else {
      let Datos_Menu = JSON.parse(localStorage.getItem("m_a_ur"));
      let menuActual = Datos_Menu.MENU;
      Menu = "";
      CargarMenu(0, menuActual);
      $("#CargarMenu").html("");
      $("#CargarMenu").append(Menu);
    }
    //}
  });

  $("#buscarMenuBtn").click(function (e) {
    var palabra = $("#buscarMenu").val().toLowerCase();
    if (palabra != "") {
      menuActual = filtrar_menu(palabra);
      Menu = "";
      CargarMenu(0, menuActual);
      $("#CargarMenu").html("");
      $("#CargarMenu").append(Menu);
    } else {
      let Datos_Menu = JSON.parse(localStorage.getItem("m_a_ur"));
      let menuActual = Datos_Menu.MENU;
      Menu = "";
      CargarMenu(0, menuActual);
      $("#CargarMenu").html("");
      $("#CargarMenu").append(Menu);
    }
  });

  //setTimeout(function(){  $("#popover486089").fadeOut(1000); }, 4000);

  $("#BuscarMenuQ").mouseover(function () {
    $("#popover486089").show();
  });

  $("#BuscarMenuQ").mouseout(function () {
    $("#popover486089").hide();
  });
});

function filtrar_menu(palabra) {
  let Datos_Menu = JSON.parse(localStorage.getItem("m_a_ur"));
  let MENU = Datos_Menu.MENU;
  let infoMenu = [];
  let infoMenu2 = [];
  let menu_unico = [];
  padres_hijos = [];

  $.each(MENU, function (indice, fila) {
    //fila.nombre.toLowerCase().indexOf(palabra) != -1

    if (
      fila.nombre.toLowerCase().indexOf(palabra) != -1 ||
      fila.descripcion.toLowerCase().indexOf(palabra) != -1 ||
      fila.url.toLowerCase().indexOf(palabra) != -1 ||
      (fila.palabras_claves.toLowerCase().indexOf(palabra) != -1 &&
        fila.palabras_claves != "")
    ) {
      fila.desplegar = 1;
      menu_padres_hijos(fila.idpadre, 0, MENU);
      menu_padres_hijos(fila.id_menu, 1, MENU);

      infoMenu = padres_hijos;
      infoMenu.unshift(fila);
    }
  });

  for (i in infoMenu) {
    if (menu_unico.indexOf(infoMenu[i].id_menu) == -1) {
      infoMenu2.push(infoMenu[i]);
      menu_unico.push(infoMenu[i].id_menu);
    }
  }

  return infoMenu2;
}

function menu_padres_hijos(id_menu, op, MENU) {
  if (op == 0) {
    $.each(MENU, function (indice, fila) {
      let id_menuLocal = parseInt(fila.id_menu);
      if (id_menuLocal == id_menu) {
        fila.desplegar = 1;
        padres_hijos.push(fila);
        menu_padres_hijos(fila.idpadre, op, MENU);
      }
    });
  } else {
    $.each(MENU, function (indice, fila) {
      let id_menuLocal = parseInt(fila.idpadre);
      if (id_menuLocal == id_menu) {
        padres_hijos.push(fila);
        menu_padres_hijos(fila.id_menu, op, MENU);
      }
    });
  }
}

function CargarMenu(id_padre, MENU) {
  if (localStorage.getItem("est_enc") == 0) {
    let html_menu = "";
    let arr_padres = [];
    if (Menu_Tiene_Hijos(id_padre, MENU) > 0) {
      $.each(MENU, function (indice, fila) {
        let id_menu = fila.id_menu;
        let padre = parseInt(fila.idpadre);

        if (padre == id_padre) {
          var open = "";
          var display = "";
          if (parseInt(fila.desplegar) == 1) {
            open = "menu-open";
            var display = "display: block";
          }

          if (padre == 0) {
            Menu +=
              '<li title="' +
              fila.nombre +
              '" aria-label="' +
              fila.nombre +
              "\" class='treeview " +
              open +
              "'>" +
              '<a href="' +
              fila.url +
              '" target = "' +
              fila.target +
              '">' +
              '<i class="fab fa-slack-hash"></i> <span>' +
              fila.nombre +
              '</span> <i class="fa fa-angle-left pull-right"></i>' +
              "</a>" +
              '<ul class="treeview-menu" style=\'' +
              display +
              "'>";
            CargarMenu(id_menu, MENU);
          } else {
            if (Menu_Tiene_Hijos(id_menu, MENU) > 0) {
              Menu +=
                '<li title="' +
                fila.nombre +
                '" aria-label="' +
                fila.nombre +
                "\" class='treeview " +
                open +
                "'><a href=\"" +
                fila.url +
                '" target = "' +
                fila.target +
                '"><i class="fas fa-sort-amount-down"></i>' +
                fila.nombre +
                '<i class="fa fa-angle-left pull-right"></i></a>' +
                '<ul class="treeview-menu" style=\'' +
                display +
                "'>";
              CargarMenu(id_menu, MENU);
            } else {
              Menu +=
                '<li title="' +
                fila.nombre +
                '" aria-label="' +
                fila.nombre +
                '"><a href="' +
                fila.url +
                '" target = "' +
                fila.target +
                '"><i class="fas fa-dot-circle"></i>' +
                fila.nombre +
                "</a></li>";
            }
          }
        }
      });
    } else {
      $.each(MENU, function (indice, fila) {
        let id_menu = fila.id;
        let padre = parseInt(fila.padre);
        if (id_menu == id_padre) {
          Menu +=
            '<li title="' +
            fila.nombre +
            '" aria-label="' +
            fila.nombre +
            '"><a href="' +
            fila.url +
            '"><i class="fa fa-slack"></i>' +
            fila.nombre +
            "</a></li>";
        }
      });
    }
    Menu += "</ul></li>";
  }
}

function Menu_Tiene_Hijos(id_padre, MENU) {
  let n = 0;
  /*let Datos_Menu = JSON.parse(localStorage.getItem("m_a_ur"));
    let MENU = Datos_Menu.MENU;*/
  $.each(MENU, function (indice, fila) {
    let padre = parseInt(fila.idpadre);
    if (padre == id_padre) {
      n += 1;
    }
  });
  return n;
}

function notificaciones() {
  fetch("modulos/val_permiso/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ accion: "consultar_noti" })
  }).then((datos) => {
    let encNum = parseInt(datos.cant_enc);
    let notiNum = parseInt(datos.cant_noti);
    if (notiNum !== null && notiNum != "" && notiNum > 0) {
      if ($("#id_noti").length > 0) {
        $("#id_noti").html(notiNum);
      } else {
        $("#notiNews").append(
          '<span class="badge red" id = "id_noti">' + notiNum + "</span>"
        );
      }
    } else {
      $("#id_noti").remove();
    }
    if (encNum !== null && encNum != "" && encNum > 0) {
      if ($("#id_enc").length > 0) {
        let valorE = parseInt($("#id_enc").html());
        let total_e = valorE - encNum;
        if (total_e > 0) {
          $("#id_noti").html(total_e);
        }
      } else {
        $("#notiEnc").append(
          '<span class="badge red" id = "id_enc">' + encNum + "</span>"
        );
      }
    } else {
      $("#id_enc").remove();
    }
  }).catch((error) => {
    console.error(error);
  });
}

function get_vars() {
  let url = location.search.replace("?", "");
  let arrUrl = url.split("&");
  let urlObj = {};
  for (let i = 0; i < arrUrl.length; i++) {
    let x = arrUrl[i].split("=");
    urlObj[x[0]] = x[1];
  }
  return urlObj;
}

/**
 * Convierte los valores de un formulario a un objeto json
 * 
 * @param {String} selector id del form del cual tomar los valores
 * @returns {Object}
 */
function formToJSON(selector) {
  let form = {};
  $(selector).find(":input[name]:checked").each(function () {
    let self = $(this);
    if (self.attr("id") != undefined) {
      let name = self.attr("id");
      if (form[name]) {
        form[name] = form[name] + "," + self.val();
      } else {
        form[name] = self.val();
      }
    }
  });
  $(selector).find(
    "input[type=text],input[type=email],input[type=number],input[type=date],input[type=password],input[type=radio],input[type=hidden],select,textarea"
  ).filter(function () {
    return this.value != "-1";
  }).each(function () {
    let self = $(this);
    if (self.attr("id") != undefined) {
      let name = self.attr("id");
      if (form[name]) {
        form[name] = form[name] + "," + self.val();
      } else {
        form[name] = self.val();
      }
    }
  });
  return form;
}

/**
 * Convierte un formulario a FormData
 * 
 * @param {String} form id del form del cual tomar los valores
 * @returns {FormData}
 */
function formToFormData(form) {
  return jsonToFormData(formToJSON(form));
}

/**
 * Convierte un objeto json a FormData
 * 
 * @param {Object} json objeto json a convertir
 * @returns {FormData}
 */
function jsonToFormData(json) {
  let formData = new FormData();
  Object.keys(json).forEach((key) => {
    // validar si es string o objeto
    if (typeof json[key] === "object") {
      formData.append(key, JSON.stringify(json[key]));
    } else {
      formData.append(key, json[key]);
    }
  });
  return formData;
}

function urlExists(url) {
  let http = jQuery.ajax({ type: "HEAD", url: url, async: !1 });
  return http.status;
}

function Error_Sistema(texto) {
  new PNotify({
    title: "Error de Sistema",
    text: texto,
    type: "error",
    addclass: "stack-bar-top",
    width: "50%",
    mouse_reset: !1,
  });
}

function Notificacion(texto, tipo) {
  PNotify.removeAll();
  new PNotify({
    title: "Mensaje del Sistema",
    text: texto,
    type: tipo,
    width: "50%",
    mouse_reset: !1,
  });
}

/**
 * Intercepta las peticiones fetch para mostrar el loader de carga
 * 
 */
const originalFetch = window.fetch; // Guarda una referencia a la fetch original

window.fetch = (...args) => {
  const url = args[0]; // El primer argumento es la URL
  const options = args[1] || {}; // El segundo argumento son las opciones
  const accion = typeof options.body === "object" ? options.body.get("accion") ?? "generico" : (typeof options.body === "string" ? JSON.parse(options.body).accion ?? "generico" : "generico");

  // console.log('Petición fetch interceptada:', url, options, accion, get.mod);

  // --- Ejecuta tu código ANTES de la petición ---
  // Por ejemplo, puedes añadir un token de autorización, un spinner de carga, etc.
  // if (options.headers) {
  //   options.headers['Authorization'] = 'Bearer your_token';
  // } else {
  //   options.headers = { 'Authorization': 'Bearer your_token' };
  // }
  // showLoadingSpinner();

  try {
    if (get.mod != "login") {
      isLoading(accion);
      validateData();
    }
    startLadda();
    return originalFetch(...args).then(response => {
      if (response.status == 401) {
        sessionCaducada();
      }
      return response;
    }).catch(error => {
      // console.error('Error en la petición fetch interceptada:', error);
      throw error;
    }).finally(() => {
      if (get.mod != "login") {
        isLoading(accion);
      }
      stopLadda();
    }); // Llama a la fetch original

    // console.log('Respuesta fetch interceptada:', response.url, response.status);

    // --- Ejecuta tu código DESPUÉS de la petición (respuesta exitosa) ---
    // Por ejemplo, puedes ocultar el spinner, manejar códigos de estado, etc.
    // isLoading(accion);
    // if (!response.ok) {
    //   console.error('Error en la respuesta:', response.statusText);
    // }
    // if (response.status == 401) {
    //   sessionCaducada();
    // }

    //return response;
  } catch (error) {
    // console.error('Error en la petición fetch interceptada:', error);

    // --- Ejecuta tu código en caso de error en la petición ---
    // Por ejemplo, mostrar un mensaje de error al usuario
    // showErrorToast(error.message);

    throw error; // Re-lanza el error para que la cadena de promesas se maneje correctamente
  } finally {
    // if (get.mod != "login") {
    //   isLoading(accion);
    // }
    // stopLadda();
    // Esto se ejecuta siempre, tanto si la petición es exitosa como si falla
    // Por ejemplo, para limpiar recursos
    // console.log('Petición fetch finalizada.');
  }
};

/**
 * Valida los datos de un formulario
 * 
 */
function validateData() {
  $("input[is-data]").each(function () {
    if ($(this).val() != "") {
      let opciones = $(this).attr("is-data").split(",");
      if (
        validar_datox(opciones[0], opciones[1], opciones[2], $(this).val()) == !1
      ) {
        let d_error = $("body").find(".appriseOverlay");
        if (!$.contains(window.document, d_error[0])) {
          let especiales = "";
          if (opciones[2] == 1) {
            especiales = ", ademas no debe contener caracteres especiales";
          }
          if (opciones[2] == 4) {
            especiales = ", ademas solo se permite letras y números";
          }
          $(this).parent().addClass("has-error");
          $(this).attr("title", "Campo no valido");
          $(this).attr("data-toggle", "popover");
          $(this).attr("data-placement", "top");
          $(this).attr(
            "data-content",
            "Debe tener un minimo de " +
            opciones[0] +
            " caracteres y maximo " +
            opciones[1] +
            especiales
          );
          $(this).popover("show");
          let top = parseInt($(".popover.top").css("margin-top")) - 35;
          // $(".popover.top").css("margin-top", top + "px"); // se desplaza la posicion si se vuelve a invocar el error
        }
      }
    }
  });
}

function startLadda() {
  try {
    if (ladda) {
      ladda.start();
    }
  } catch (error) {
    console.log("Error al iniciar ladda: ", error);
  }
}

function stopLadda() {
  try {
    if (ladda) {
      ladda.stop();
    }
  } catch (error) {
    console.log("Error al detener ladda: ", error);
  }
}

function onAjaxSend(accion) {
  startLadda();
  if (get.mod != "login") {
    isLoading(accion);
  }
  validateData();
}

function getRequestAccion(settings) {
  return settings.url + "?" + (typeof settings.data === "object" ? settings.data.toString() : settings.data);
}

$(document).ajaxSend(function (event, request, settings) {
  onAjaxSend(getRequestAccion(settings));
});

function sessionCaducada() {
  hideLoader();
  BootstrapDialog.show({
    title: "La sesión ha caducado!",
    type: BootstrapDialog.TYPE_SUCCESS,
    closable: false,
    closeByBackdrop: false,
    closeByKeyboard: false,
    message:
      "<center>Es necesario volver a iniciar sesión para continuar con los procesos pendientes.</center>",
    buttons: [
      {
        label: "Iniciar Sesión",
        cssClass: "btn-info",
        action: function (dialog) {
          //dialog.close();
          window.location = "?cerrar=S";
        },
      },
    ],
    onshonw: function (dialog) { },
  });
  return;
}

function onAjaxComplete(accion, data) {
  isLoading(accion);
  $(".btn-primary").removeAttr("disabled", "disabled");
  stopLadda();
  if (get.mod != "login" && get.mod != "recopass") {
    if (data == '{"estadoSesion":201}') {
      sessionCaducada();
    }
  }
}

$(document).ajaxComplete(function (event, request, settings) {
  onAjaxComplete(getRequestAccion(settings), request.responseText);
});

$(document).ajaxError(function (event, xhr, ajaxOptions, thrownError) {
  isLoading(accion);
  $(".btn-primary").removeAttr("disabled", "disabled");
  stopLadda();
  //if (xhr.statusText == "abort") {}
});

function confirma_rest(data, accion) {
  if (
    data.indexOf("<b>Parse error</b>:") > -1 ||
    data.indexOf("<b>Mensaje: </b>") > -1
  ) {
    Error_Sistema(
      "<pre>" + data + "<br><b>Accion Rest: </b>" + accion + "</pre>"
    );
    return !1;
  } else if (data == "null") {
    Notificacion(
      "La consulta no devolvio ningun dato.<br><b>Accion Rest: </b>" + accion,
      "warning"
    );
    return !1;
  } else {
    return data;
  }
}

function Validar_Permiso(modulo) {
  return true;
  let n = 0;
  let Datos_Menu = JSON.parse(localStorage.getItem("m_a_ur"));
  let MENU = Datos_Menu.MENU;
  modulo = "?mod=" + modulo;
  $.each(MENU, function (indice, fila) {
    let menu = fila.url;
    if (menu == modulo) {
      $("titulomod").html(fila.nombre);
      $("descripcionmod").html("<small>" + fila.nombre + "</small>");
      n += 1;
    }
  });
  if (modulo == "?mod=cambio_pass") {
    n = 1;
  }
  if (n == 0) {
    location.href = "?mod=principal&per=N";
  }
}

function Cargar_Departamento_Ciudad(select_depto, select_ciudad) {
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Departamento_Ciudad"
    })
  }).then(response => response.json()).then(data => {
    if (confirma_rest(data, "Retornar_Departamento_Ciudad")) {
      if (data != 0 || data != "") {
        let html_selectDepartamento =
          "<option selected='selected' value=''>SELECCIONAR</option>";
        $.each(data.departamento, function (index, fila) {
          html_selectDepartamento +=
            "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
        });
        $("#" + select_depto).change(function () {
          let html_selectCiudad =
            "<option selected='selected' value=''>SELECCIONAR</option>";
          let id_depto = $(this).val();
          $.each(data.ciudad, function (index, fila) {
            if (fila.id_departamento == id_depto) {
              html_selectCiudad +=
                "<option value='" +
                fila.id +
                "'>" +
                fila.nombre +
                "</option>";
            }
          });
          $("#" + select_ciudad)
            .html(html_selectCiudad)
            .change();
        });
        $("#" + select_depto)
          .html(html_selectDepartamento)
          .change();
      }
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}

function Cargar_Rutas_Circuitos(select_ruta, select_circuito) {
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Rutas_Circuitos"
    })
  }).then(response => response.json()).then(data => {
    if (confirma_rest(data, "Retornar_Rutas_Circuitos")) {
      if (data != 0 || data != "") {
        let html_ruta = "<option value=''>SELECCIONAR</option>";
        $.each(data.rutas, function (index, fila) {
          html_ruta +=
            "<option value='" +
            fila.id +
            "'>" +
            fila.descripcion +
            "</option>";
        });
        $("#" + select_ruta).change(function () {
          let html_circuito =
            "<option selected='selected' value=''>SELECCIONAR</option>";
          let id_ruta = $(this).val();
          $.each(data.circuitos, function (index, fila) {
            if (fila.id_ruta == id_ruta) {
              html_circuito +=
                "<option value='" +
                fila.id +
                "'>" +
                fila.nombre +
                "</option>";
            }
          });
          $("#" + select_circuito)
            .html(html_circuito)
            .change();
        });
        $("#" + select_ruta)
          .html(html_ruta)
          .change();
      }
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}

function Funcion_Cargar_Bodegas(select_bodega, tipo) {
  if (tipo == undefined) {
    tipo = "";
  }
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Bodegas",
      tipo: tipo
    })
  }).then(response => response.json()).then(data => {
    if (data != 0 || data != "") {
      let html_bodega = "<option value=''>Seleccionar...</option>";
      $.each(data, function (index, fila) {
        html_bodega +=
          "<option value='" +
          fila.warehouse_id +
          "'>" +
          fila.name +
          "</option>";
      });
      $("#" + select_bodega)
        .html(html_bodega)
        .change();
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}

function funcion_cargar_regionales(select_regional, tipo) {
  if (tipo == undefined) {
    tipo = 0;
  }
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Regionales",
      tipo: tipo
    })
  }).then(response => response.json()).then(data => {
    if (data != 0 || data != "") {
      let html_regional =
        "<option selected='selected' value=''>SELECCIONAR</option>";
      $.each(data, function (index, fila) {
        html_regional +=
          "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
      });
      $("#" + select_regional)
        .html(html_regional)
        .change();
    }
  }).catch(error => {
    console.error('Error:', error);
  });
  return !0;
}

function Cargar_regionales_Rutas_Circuitos(
  select_ruta,
  select_circuito,
  regional,
  tipo
) {
  if (tipo == undefined) {
    tipo = 0;
  }
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Rutas_Circuitos",
      tipo: tipo
    })
  }).then(response => response.json()).then(data => {
    if (confirma_rest(data, "Retornar_Rutas_Circuitos")) {
      if (data != 0 || data != "") {
        let html_ruta = "<option value=''>SELECCIONAR</option>";
        $.each(data.rutas, function (index, fila) {
          if (fila.id_regional == regional) {
            html_ruta +=
              "<option value='" +
              fila.id +
              "'>" +
              fila.descripcion +
              "</option>";
          }
        });
        $("#" + select_ruta).change(function () {
          let html_circuito = "<option value=''>SELECCIONAR</option>";
          let id_territorio = $(this).val();
          $.each(data.circuitos, function (index, fila) {
            if (fila.id_territorio == id_territorio) {
              html_circuito +=
                "<option value='" +
                fila.id +
                "'>" +
                fila.nombre +
                "</option>";
            }
          });
          $("#" + select_circuito)
            .html(html_circuito)
            .change();
        });
        $("#" + select_ruta)
          .html(html_ruta)
          .change();
      }
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}

function funcion_cargar_distribuidores(select_distri, tipo) {
  if (tipo == undefined) {
    tipo = 0;
  }
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Distribuidores",
      tipo: tipo
    })
  }).then(response => response.json()).then(data => {
    if (data != 0 || data != "") {
      let html_distri =
        "<option selected='selected' value=''>SELECCIONAR</option>";
      $.each(data, function (index, fila) {
        html_distri +=
          "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
      });
      $("#" + select_distri)
        .html(html_distri)
        .change();
    }
  }).catch(error => {
    console.error('Error:', error);
  });
  return !0;
}

function funcion_cargar_circuitos(select_cir, estado = "") {
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Circuitos",
      estado: estado
    })
  }).then(response => response.json()).then(data => {
    if (data != 0 || data != "") {
      let html_cir =
        "<option selected='selected' value=''>SELECCIONAR</option>";
      $.each(data, function (index, fila) {
        html_cir +=
          "<option value='" + fila.route + "'>" + fila.name_route + "</option>";
      });
      $("#" + select_cir)
        .html(html_cir)
        .change();
    }
  }).catch(error => {
    console.error('Error:', error);
  });
  return !0;
}

function funcion_cargar_usuarios(select_usuario, tipo) {
  if (tipo == undefined) {
    tipo = "";
  }
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Usuarios",
      tipo: tipo
    })
  }).then(response => response.json()).then(data => {
    if (data != 0 || data != "") {
      data = JSON.parse(data);
      let html_usuario =
        "<option selected='selected' value=''>SELECCIONAR</option>";
      $.each(data, function (index, fila) {
        html_usuario +=
          "<option value='" + fila.id + "'>" + fila.nombre_c + "</option>";
      });
      $("#" + select_usuario)
        .html(html_usuario)
        .change();
    }
  }).catch(error => {
    console.error('Error:', error);
  });
  return !0;
}

function funcion_cargar_perfiles(select_perfil, tipo) {
  if (tipo == undefined) {
    tipo = "";
  }
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Perfiles",
      tipo: tipo
    })
  }).then(response => response.json()).then(data => {
    if (data != 0 || data != "") {
      let html_perfil =
        "<option selected='selected' value=''>SELECCIONAR</option>";
      $.each(data, function (index, fila) {
        html_perfil +=
          "<option value='" + fila.id + "'>" + fila.name + "</option>";
      });
      $("#" + select_perfil)
        .html(html_perfil)
        .change();
    }
  }).catch(error => {
    console.error('Error:', error);
  });
  return !0;
}

function funcion_cargar_canales_distri(select_canal_d, tipo) {
  if (tipo == undefined) {
    tipo = "";
  }
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_Canales",
      tipo: tipo
    })
  }).then(response => response.json()).then(data => {
    if (data != 0 || data != "") {
      let html_canal_d =
        "<option selected='selected' value=''>SELECCIONAR</option>";
      $.each(data, function (index, fila) {
        html_canal_d +=
          "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
      });
      $("#" + select_canal_d)
        .html(html_canal_d)
        .change();
    }
  }).catch(error => {
    console.error('Error:', error);
  });
  return !0;
}

function validar_datox(cmin, cmax, special, cadena) {
  let tamanio = 0;
  tamanio = cadena.length;
  if (tamanio < cmin || tamanio > cmax) {
    return !1;
  } else {
    if (special == 1) {
      let caracteres = [
        ";",
        "'",
        "!",
        '"',
        "#",
        "$",
        "%",
        "(",
        ")",
        "*",
        "+",
        "?",
        "=",
        ".",
        "/",
        "|",
        "&",
        ":",
        "<",
        ">",
        "¡",
        "{",
        "}",
        "[",
        "]",
        "_",
      ];

      for (let i = 0; i < caracteres.length; i++) {
        cadena = cadena.toUpperCase();

        if (cadena.indexOf(caracteres[i]) > -1) return false;
      }
    } else {
      if (special == 2) {
        for (let i = 0; i < cadena.length; i++) {
          if (cadena.charCodeAt(i) < 48 || cadena.charCodeAt(i) > 57)
            return false;
        }
      } else if (special == 3) {
        for (let i = 0; i < cadena.length; i++) {
          cadena = cadena.toUpperCase();

          if (cadena.charCodeAt(i) < 65 || cadena.charCodeAt(i) > 90)
            return false;
        }
      } else if (special == 4) {
        let validar = !1;
        let espacio = !1;
        let numero = !0;
        let letras = !0;
        let validacion = [
          ";",
          "'",
          "|",
          "!",
          "º",
          "ª",
          "\\",
          "",
          '"',
          "#",
          "$",
          "¢",
          "%",
          "∞",
          "&",
          "¬",
          "/",
          "÷",
          "(",
          ")",
          "=",
          "“",
          "”",
          "?",
          "´",
          "¡",
          "‚",
          "¿",
          "^",
          "`",
          "[",
          "*",
          "+",
          "]",
          "´",
          "¨",
          "{",
          "ç",
          "}",
          "Ç",
          "„",
          ".",
          "…",
          ":",
          "_",
          "-",
          "<",
          ">",
        ];

        cadena = cadena.toLowerCase();

        for (let z = 0; z < validacion.length; z++) {
          if (cadena.indexOf(validacion[z]) != -1 && validacion[z] != "") {
            validar = true;
          }
        }

        for (let i = 0; i < cadena.length; i++) {
          if (
            (cadena.charCodeAt(i) >= 97 && cadena.charCodeAt(i) <= 122) ||
            (cadena.charCodeAt(i) >= 65 && cadena.charCodeAt(i) <= 90)
          ) {
            letras = false;
          }

          if (cadena.charCodeAt(i) >= 48 && cadena.charCodeAt(i) <= 57) {
            numero = false;
          }

          if (cadena.charAt(i) == " ") {
            espacio = true;
          }
        }

        if (validar || espacio || numero || letras) {
          return false;
        }
      }
    }
  }

  return true;
}

function ExportarCSV(JSONData, ShowLabel, fileName, columnas) {
  let arrData = typeof JSONData != "object" ? JSON.parse(JSONData) : JSONData;
  let CSV = "";
  let row = "";
  let universalBOM = "\uFEFF";
  let link;

  if (columnas == undefined) {
    columnas = "";
  }

  if (ShowLabel != "" && ShowLabel != true) {
    let datos = ShowLabel.split(",");

    for (let i = 0; i < datos.length; i++) {
      row += datos[i] + ";";
    }

    row = row.slice(0, -1);
    CSV += row + "\r\n";
  } else {
    for (let index in arrData[0]) {
      row += index + ";";
    }

    row = row.slice(0, -1);
    CSV += row + "\r\n";
  }
  if (columnas != "" && columnas != !0) {
    let col = columnas.split(",");

    for (let i = 0; i < arrData.length; i++) {
      row = "";

      for (let a = 0; a < col.length; a++) {
        let index = col[a];

        row += "" + arrData[i][index] + ";";
      }
      row = row.substring(0, row.length - 1);
      CSV += row + "\r\n";
    }
  } else {
    for (let i = 0; i < arrData.length; i++) {
      row = "";

      for (let index in arrData[i]) {
        row += "" + arrData[i][index] + ";";
      }

      row = row.substring(0, row.length - 1);
      CSV += row + "\r\n";
    }
  }

  if (CSV === "") {
    Notificacion("No se encontro información para el reporte", "error");

    return;
  }

  link = document.createElement("a");
  link.setAttribute(
    "href",
    "data:text/csv; charset=utf-8," + encodeURIComponent(universalBOM + CSV)
  );
  link.setAttribute("download", fileName);
  document.body.appendChild(link);
  link.click();
}

function format(n, c, d, t) {
  (c = isNaN((c = Math.abs(c))) ? 2 : c),
    (d = d == undefined ? "." : d),
    (t = t == undefined ? "," : t),
    (s = n < 0 ? "-" : ""),
    (i = parseInt((n = Math.abs(+n || 0).toFixed(c))) + ""),
    (j = (j = i.length) > 3 ? j % 3 : 0);
  return (
    s +
    (j ? i.substr(0, j) + t : "") +
    i.substr(j).replace(/(\d{3})(?=\d)/g, "$1" + t) +
    (c
      ? d +
      Math.abs(n - i)
        .toFixed(c)
        .slice(2)
      : "")
  );
}

function getLocation() {
  if (navigator.geolocation) {
    navigator.geolocation.getCurrentPosition(showPosition, showError, {});
  } else {
    alert("Geolocation is not supported by this browser");
  }
}

function showPosition(position) {
  let array = [];

  array.push({ lat: position.coords.latitude, lng: position.coords.longitude });
  localStorage.setItem("position", JSON.stringify(array));
}

function showError(error) {
  switch (error.code) {
    case error.PERMISSION_DENIED:
      BootstrapDialog.show({
        title: "MENSAJE DE APLICACION",
        message:
          '<div class="text-center">Para continuar debe permitirnos obtener los datos de la posicion actual. <br><br> Por favor active la opcion en el navegador y vuelva a intentarlo</div>',
        type: BootstrapDialog.TYPE_DANGER,
        closable: !0,
        draggable: !1,
        buttons: [
          {
            label:
              '<i class="glyphicon glyphicon-globe"></i> Continuar Sin Posicion',
            cssClass: "btn-default",
            action: function (dialog) {
              dialog.close();
            },
          },
        ],
      });
      break;

    case error.POSITION_UNAVAILABLE:
      BootstrapDialog.show({
        title: "MENSAJE DE APLICACION",
        message:
          '<div class="text-center">No se encontraron datos de localizacion, compruebe que el GPS este activo.</div>',
        type: BootstrapDialog.TYPE_DANGER,
        closable: !0,
        draggable: !1,
        buttons: [
          {
            label:
              '<i class="glyphicon glyphicon-globe"></i> Continuar Sin Posicion',
            cssClass: "btn-default",
            action: function (dialog) {
              dialog.close();
            },
          },
        ],
      });
      break;

    case error.TIMEOUT:
      BootstrapDialog.show({
        title: "MENSAJE DE APLICACION",
        message:
          '<div class="text-center">Error al intentar obtener los datos de la posicion actual.</div>',
        type: BootstrapDialog.TYPE_DANGER,
        closable: !0,
        draggable: !1,
        buttons: [
          {
            label:
              '<i class="glyphicon glyphicon-globe"></i> Continuar Sin Posicion',
            cssClass: "btn-default",
            action: function (dialog) {
              dialog.close();
            },
          },
        ],
      });
      break;

    case error.UNKNOWN_ERROR:
      BootstrapDialog.show({
        title: "MENSAJE DE APLICACION",
        message:
          '<div class="text-center">No se puede ingresar, error al intentar tomar la posicion actual.</div>',
        type: BootstrapDialog.TYPE_DANGER,
        closable: !0,
        draggable: !1,
        buttons: [
          {
            label:
              '<i class="glyphicon glyphicon-globe"></i> Continuar Sin Posicion',
            cssClass: "btn-default",
            action: function (dialog) {
              dialog.close();
            },
          },
        ],
      });
      break;
  }
}

function validar_numeros(e, id, tipo, digitos = 0) {
  let teclaPulsada = window.event ? window.event.keyCode : e.which;
  let valor = document.getElementById(id).value;
  if (tipo == -1) {
    if (teclaPulsada == 45 && valor.indexOf("-") == -1) {
      document.getElementById(id).value = "-" + valor;
    }
    if (
      teclaPulsada == 13 ||
      (teclaPulsada == 46 && valor.indexOf(".") == -1)
    ) {
      return !0;
    }
  } else if (tipo == 1) {
    if (
      teclaPulsada == 13 ||
      (teclaPulsada == 46 && valor.indexOf(".") == -1)
    ) {
      return !0;
    }
  } else if (tipo == 2) {
    if (valor.length >= digitos) {
      return !1;
    }
    if ((teclaPulsada > 47 && teclaPulsada < 58) || teclaPulsada == 13) {
      return !0;
    } else {
      return !1;
    }
  }
  return /\d/.test(String.fromCharCode(teclaPulsada));
}

function Cargar_categorias_subcategorias(select_categoria) {
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "Retornar_categorias_subcategorias"
    })
  }).then(response => response.json()).then(data => {
    if (data != null || data != "") {
      let html_categoria = "<option value=''>SELECCIONAR</option>";
      $.each(data.categorias, function (index, fila) {
        html_categoria +=
          "<option value='" + fila.id + "'>" + fila.nombre + "</option>";
      });
      $("#" + select_categoria)
        .html(html_categoria)
        .change();
    }
  }).catch(error => {
    console.error('Error:', error);
  });
  return !0;
}

function hex2a(hex) {
  let str = "";
  for (let i = 0; i < hex.length; i += 2)
    str += String.fromCharCode(parseInt(hex.substr(i, 2), 16));
  return str;
}

function geo_detalle_visita(id_check, repor_super = "false") {
  $("#cargar_visita_detalle").show();
  $("#modal_geo_visita").modal("show");
  loader_dw = $("#modal_geo_visita")
    .loadingIndicator({ useImage: !1 })
    .data("loadingIndicator");
  loader_dw.show();
  let img = "";
  let zona;
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "geo_detalle_visita",
      id_check: id_check,
      repor_super: repor_super,
    })
  }).then(response => response.json()).then(data => {
    let info_visita = data.datos_visita;
    $.each(info_visita, function (index, row) {
      $("#id_pos").html(row.punto);
      $("#distri_nombre").html(row.nom_distri);
      $("#nom_vende").html(row.nombre_usuario);
      $("#fecha_visita").html(row.fecha_check);
      $("#hora_ini_visita").html(row.hora_ini);
      $("#hora_fin_visita").html(row.hora_fin);
      if (global_codigo_personalizado == 1) {
        setTimeout(() => {
          $(".content_label_codigo_personalizado").html(`<div class="panel-heading text-center">
                                                            <label>${global_nombre_personalizado}:</label> 
                                                            <span>${row.codigo}</span>
                                                          </div>`)
          const label_codigo_personalizado = document.getElementsByClassName("content_label_codigo_personalizado")[0]
          if (label_codigo_personalizado) label_codigo_personalizado.className = "form-group col-md-2 content_label_codigo_personalizado"
        }, 500);
      }
      if (row.fecha_sincroniza != "N/A") {
        $(".fecha_sincroniza").show();
        $(".fecha_sincroniza_l").show();
        $(".fecha_sincroniza").html(row.fecha_sincroniza);
      } else {
        $(".fecha_sincroniza").hide();
        $(".fecha_sincroniza_l").hide();
      }
      if (row.hora_sincroniza != "N/A") {
        $(".hora_sincroniza").show();
        $(".hora_sincroniza_l").show();
        $(".hora_sincroniza").html(row.hora_sincroniza);
      } else {
        $(".hora_sincroniza").hide();
        $(".hora_sincroniza_l").hide();
      }
      $("#tiempo_visita").html(row.tiempo_visi);
      $("#motivo_visita").html(row.motivo_visi);
      $("#obs_visita").html(row.obs_visi);
      $("#nombrepdv").html(row.nombre_pdv);
      $("#circuito").html(row.circuito);
      $("#ruta").html(row.ruta);
      $("#obs_visita").show();

      if (row.posicion_simulada == "1") {
        $("#geo-referencia-simulada").show();
      } else {
        $("#geo-referencia-simulada").hide();
      }

      $("#visita_realizada_punto").html(row.visitaEnPunto);
      $("#visita_realizada_qr").html(row.qr);

      if (row.observacion_qr != "") {
        $("#obs_qr").html(row.observacion_qr);
        $("#obs_qr").show();
      } else {
        $("#obs_qr").hide();
      }

      zona = row.id_zona;
      if (row.tipo == 1) {
        $("#extraruta_check").attr("style", "display: none;");
        $("#duplicada_check").attr("style", "display: none;");
        $("#ruta_check").attr("style", "");
      } else if (row.tipo == 2) {
        $("#ruta_check").attr("style", "display: none;");
        $("#duplicada_check").attr("style", "display: none;");
        $("#extraruta_check").attr("style", "");
      } else if (row.tipo == 3) {
        $("#ruta_check").attr("style", "display: none;");
        $("#extraruta_check").attr("style", "display: none;");
        $("#duplicada_check").attr("style", "");
      } else {
        $("#ruta_check").attr("style", "display: none;");
        $("#duplicada_check").attr("style", "display: none;");
        $("#extraruta_check").attr("style", "display: none;");
      }
      if (row.obs_visi == "") {
        $("#obs_visita").hide();
      }
      $("#mapa_visita").attr(
        "src",
        "mapas/usuario-punto.php?longPoint=" +
        parseFloat(row.long_pdv) +
        "&latPoint=" +
        parseFloat(row.lat_pdv) +
        "&longUser=" +
        parseFloat(row.lng) +
        "&latUser=" +
        parseFloat(row.lat) +
        "&precisionUser=" +
        parseFloat(row.margen_e) +
        "&precisionPdv=" +
        parseFloat(row.margen_e)
      );
      img = "static/img/not_img.png";
      if (row.evidencia_validar == "1") {
        if (row.img_evidencia != "") {
          img = "data:image/jpg;base64," + row.img_evidencia;
        }
        $("#ver_evidencia").show();
      } else {
        $("#ver_evidencia").hide();
      }
      $("#no_imagen").html("");
      if (row.evidencia_eliminada == 1) {
        $("#no_imagen").html(
          'El usuario: <span style="color:red">' +
          $("#nom_vende").text() +
          "</span> ha eliminado la imagen"
        );
      }
      $("#img_evidencia").html(
        '<img id="img_evid" src = "' +
        img +
        '" border="0" style="width: 250px;" />'
      );
    });
    if ($.fn.DataTable.isDataTable("#resumen_visita")) {
      dtable2.clear();
      dtable2.destroy();
    }
    let columnas = [
      { data: "pn" },
      { data: "producto" },
      { data: "tipo_compra" },
      { data: "cantidad" },
      { data: "pedido" },
    ];
    columnas_defect = [];
    funtion_table = "";
    dtable2 = $("#resumen_visita").DataTable({
      data: data.datos_ventas,
      bFilter: !1,
      columns: columnas,
      columnDefs: columnas_defect,
      fnDrawCallback: funtion_table,
    });
    id_checks.push(id_check);
    cargar_frecuencia(zona, id_check);
    loader_dw.hide();
    $(".loading-indicator-wrapper").css("display", "none");
  }).catch(error => {
    console.error('Error:', error);
  });
}

function geo_detalle_visita_operador(id_check) {
  $("#cargar_visita_detalle_operador").show();
  $("#modal_geo_visita_operador").modal("show");
  loader_dw = $("#modal_geo_visita_operador")
    .loadingIndicator({ useImage: !1 })
    .data("loadingIndicator");
  loader_dw.show();
  let img = "";
  let zona;
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "geo_detalle_visita_operador",
      id_check: id_check,
    })
  }).then(response => response.json()).then(data => {
    let info_visita = data.datos_visita;
    $.each(info_visita, function (index, row) {
      $("#id_pos_operador").html(row.punto);
      $("#distri_nombre_operador").html(row.nom_distri);
      $("#nom_vende_operador").html(row.nombre_usuario);
      $("#fecha_visita_operador").html(row.fecha_check);
      $("#hora_ini_visita_operador").html(row.hora_ini);
      //$("#hora_fin_visita_operador").html(row.hora_fin);
      $("#tiempo_visita_operador").html(row.tiempo_visi);
      //$("#frecuencia_operador").html(row.frecuencia_punto);
      $("#motivo_visita_operador").html(row.motivo_visi);
      $("#obs_visita_operador").html(row.obs_visi);

      $("#nombrepdv_operador").html(row.nombre_pdv);
      $("#circuito_operador").html(row.circuito);
      $("#ruta_operador").html(row.ruta);
      $("#obs_visita_operador").show();

      /*if (row.extra_ruta == 1) {
               $("#ruta_check_operador").attr("style", "display: none;");
               $("#duplicada_check_operador").attr("style", "display: none;");
               $("#extraruta_check_operador").attr("style", "");
               
           }else if(row.extra_ruta == 0){*/
      $("#extraruta_check_operador").attr("style", "display: none;");
      $("#duplicada_check_operador").attr("style", "display: none;");
      $("#ruta_check_operador").attr("style", "");

      // }

      /*if (row.tipo == 3) {
              $("#ruta_check_operador").attr("style", "display: none;");
              $("#extraruta_check_operador").attr("style", "display: none;");
              $("#duplicada_check_operador").attr("style", "");
          } else {
              $("#ruta_check_operador").attr("style", "display: none;");
              $("#duplicada_check_operador").attr("style", "display: none;");
              $("#extraruta_check_operador").attr("style", "display: none;");
          }*/
      zona = row.id_zona;
      if (row.obs_visi == "") {
        $("#obs_visita_operador").hide();
      }
      $("#mapa_visita_operador").attr(
        "src",
        "funciones/mapa.php?lat_visita=" +
        parseFloat(row.lat) +
        "&lng_visita=" +
        parseFloat(row.lng) +
        "&lat_pdv=" +
        parseFloat(row.lat_pdv) +
        "&long_pdv=" +
        parseFloat(row.long_pdv) +
        "&margen_e=" +
        parseFloat(row.margen_e) +
        "&margen_pdv=" +
        parseFloat(row.margen_pdv)
      );
      img = "static/img/not_img.png";
    });

    /************************* FRECUENCIA************ */

    data_frec = data.data_frecuencia;
    var frecuencia = "";
    if (data_frec[0].lunes == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "lunes";
    }
    if (data_frec[0].martes == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "martes";
    }
    if (data_frec[0].miercoles == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "miercoles";
    }
    if (data_frec[0].jueves == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "jueves";
    }
    if (data_frec[0].viernes == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "viernes";
    }
    if (data_frec[0].sabado == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "sabado";
    }
    if (data_frec[0].domingo == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "domingo";
    }
    if (frecuencia == "") {
      frecuencia = data_frec[0].fecha;
    }
    $("#frecuencia_operador").html(frecuencia);

    /********************** PROCESOS REALIZADOS */

    if ($.fn.DataTable.isDataTable("#proceso_operador")) {
      dtable2.clear();
      dtable2.destroy();
    }
    let columnas = [{ data: "tipo_encuesta" }, { data: "hora" }];
    columnas_defect = [];
    funtion_table = function () { };
    dtable2 = $("#proceso_operador").DataTable({
      data: data.datos_visita_detalle,
      bFilter: !1,
      columns: columnas,
      columnDefs: columnas_defect,
      fnDrawCallback: funtion_table,
    });

    id_checks.push(id_check);
    loader_dw.hide();
  }).catch(error => {
    console.error('Error:', error);
  });
}

function cargar_mapa_visitas_operador() {
  $("#cargar_visita_detalle_operador").load(
    "template/vista_detalle_visita_operador.html"
  );
}

function inactivar_csv() {
  if ($("#consulta").is(":visible")) {
    $("#CSV").hide();
  }
}

var validar_modal_cerrar = 0;

$("body").on("click", ".modal_img", function () {
  if ($("#myModal_imagen").is(":visible")) {
    $("#myModal_imagen").hide();
  }
});
$("body").on("click", "#img_evid", function () {
  $("#myModal_imagen").show();
  $("#img01").attr("src", $("#img_evid").attr("src"));
});
$("body").on("click", ".modal-content", function (e) {
  validar_modal_cerrar = 1;
});
$("body").on("click", ".modal", function (e) {
  if (validar_modal_cerrar == 0) {
    $("#modal_geo_visita").modal("hide");
  } else {
    validar_modal_cerrar = 0;
  }
});

function obtener_proceso(id_check) {
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "obtener_proceso",
      id_check: id_check,
    })
  }).then(response => response.json()).then(data => {
    if ($.fn.DataTable.isDataTable("#proceso")) {
      dtable3.clear();
      dtable3.destroy();
    }
    let columnas = [{ data: "PROCESO" }, { data: "hora_proceso" }];
    columnas_defect = [];
    funtion_table = function () { };
    dtable3 = $("#proceso").DataTable({
      order: [[1, "asc"]],
      data: data,
      bFilter: !1,
      columns: columnas,
      columnDefs: columnas_defect,
      fnDrawCallback: funtion_table,
    });
    loader_dw.hide();
  }).catch(error => {
    console.error('Error:', error);
  });
}

function cargar_frecuencia(zona, id_check) {
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "cargar_frecuencia",
      zona: zona,
    })
  }).then(response => response.json()).then(data => {
    let frecuencia = "";
    if (data[0].lunes == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "lunes";
    }
    if (data[0].martes == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "martes";
    }
    if (data[0].miercoles == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "miercoles";
    }
    if (data[0].jueves == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "jueves";
    }
    if (data[0].viernes == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "viernes";
    }
    if (data[0].sabado == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "sabado";
    }
    if (data[0].domingo == 1) {
      if (frecuencia != "") {
        frecuencia += ", ";
      }
      frecuencia += "domingo";
    }
    if (frecuencia == "") {
      frecuencia = data[0].fecha;
    }
    $("#frecuencia").html(frecuencia);
    obtener_proceso(id_check);
  }).catch(error => {
    console.error('Error:', error);
  });
}

function eliminar_imagenes() {
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "eliminar_imagenes",
      id_checks: id_checks,
    })
  }).then(response => response.json()).then(data => {
    return data;
  }).catch(error => {
    console.error('Error:', error);
  });
}

function getUrl() {
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      accion: "url-reportes",
    }),
  }).then((data) => {
    return (Url_reportes = data);
  }).catch((error) => {
    console.error(error);
  });
}

async function getImg() {
  var url = "";
  let data = await fetch("funciones/controlador.php", {
    method: "POST",
    body: JSON.stringify({
      accion: "retornar_ruta_img",
    })
  });
  if (!data.ok) {
    console.error("Error al obtener la ruta de la imagen", data.statusText);
  }
  url = await data.text();
  return url + "visit/";
}

//Validar longitud texto datatables
var estado_texto_global = false;
var id_texto_global = "";
var id_columna_global = "";
var nombre_tabla_global = "";

function verTextoCortoCompletoGlobal(tabla = null, id = null, columna = null) {
  $(".texto_completo_global").each(function () {
    var id = $(this).attr("id_texto");
    var columna = $(this).attr("columna");
    var tabla = $(this).attr("tabla");
    $("#texto_corto_global_" + tabla + "_" + columna + "_" + id).show();
    $("#texto_completo_global_" + tabla + "_" + columna + "_" + id).hide();
    estado_texto_global = false;
  });

  if (
    id_texto_global == id &&
    id_columna_global == columna &&
    nombre_tabla_global == tabla
  ) {
    $(
      "#texto_corto_global_" +
      tabla +
      "_" +
      id_columna_global +
      "_" +
      id_texto_global
    ).show();
    $(
      "#texto_completo_global_" +
      tabla +
      "_" +
      id_columna_global +
      "_" +
      id_texto_global
    ).hide();
    estado_texto_global = false;
    id_texto_global = "";
    nombre_tabla_global = "";

    if ($.fn.DataTable.isDataTable("#" + tabla)) {
      setTimeout(() => {
        $("#" + tabla)
          .DataTable()
          .columns.adjust();
      }, 100);
    }

    return;
  }

  if (id_texto_global != id && nombre_tabla_global == tabla) {
    estado_texto_global = false;
    $(
      "#texto_corto_global_" + tabla + "_" + columna + "_" + id_texto_global
    ).show();
    $(
      "#texto_completo_global_" + tabla + "_" + columna + "_" + id_texto_global
    ).hide();

    $(
      "#texto_corto_global_" +
      tabla +
      "_" +
      id_columna_global +
      "_" +
      id_texto_global
    ).show();
    $(
      "#texto_completo_global_" +
      tabla +
      "_" +
      id_columna_global +
      "_" +
      id_texto_global
    ).hide();
  }

  id_texto_global = id;
  id_columna_global = columna;
  nombre_tabla_global = tabla;

  if (estado_texto_global) {
    estado_texto_global = false;
    $("#texto_corto_global_" + tabla + "_" + columna + "_" + id).show();
    $("#texto_completo_global_" + tabla + "_" + columna + "_" + id).hide();
  } else {
    estado_texto_global = true;
    $("#texto_corto_global_" + tabla + "_" + columna + "_" + id).hide();
    $("#texto_completo_global_" + tabla + "_" + columna + "_" + id).show();
  }

  if ($.fn.DataTable.isDataTable("#" + tabla)) {
    setTimeout(() => {
      $("#" + tabla)
        .DataTable()
        .columns.adjust();
    }, 100);
  }
}

function longitudTextoGlobal(
  tabla = null,
  id = null,
  texto = null,
  columna = null,
  tipo = null
) {
  //Tipo 1 = Datatable principal
  //Tipo 2 = Datatable modal
  if (tipo == 1) {
    limite = 23;
  }

  if (tipo == 2) {
    limite = 15;
  }

  //Numérico
  if (tipo == 3) {
    limite = 15;
  }

  if (id == null || columna == null) {
    $(".texto_completo_global").each(function () {
      var id = $(this).attr("id_texto");
      var columna = $(this).attr("columna");
      var tabla = $(this).attr("tabla");
      $("#texto_corto_global_" + tabla + "_" + columna + "_" + id).show();
      $("#texto_completo_global_" + tabla + "_" + columna + "_" + id).hide();
      estado_texto_global = false;
    });

    return;
  }

  //Texto
  if ((tipo == 1 || tipo == 2) && texto.length <= limite) {
    return (
      '<div><span style="display:none">' +
      texto +
      "</span><span>" +
      texto +
      "</span></div>"
    );
  }

  //Numérico
  if (tipo == 3 && texto.length <= limite) {
    return "$" + texto;
  }

  var textCorto = texto.substring(0, limite) + "...";

  //Tipo texto
  if (tipo == 1 || tipo == 2) {
    return (
      "<div>" +
      '<span style="display:none">' +
      textCorto +
      "</span>" +
      "<span " +
      'id="texto_corto_global_' +
      tabla +
      "_" +
      columna +
      "_" +
      id +
      '" ' +
      'style="cursor:pointer;" ' +
      "onclick=\"verTextoCortoCompletoGlobal('" +
      tabla +
      "'," +
      id +
      "," +
      columna +
      ')">' +
      textCorto +
      "</span>" +
      "<span " +
      'id="texto_completo_global_' +
      tabla +
      "_" +
      columna +
      "_" +
      id +
      '" ' +
      'class="texto_completo_global" ' +
      'tabla="' +
      tabla +
      '" ' +
      'id_texto="' +
      id +
      '" ' +
      'columna="' +
      columna +
      '" ' +
      "onclick=\"verTextoCortoCompletoGlobal('" +
      tabla +
      "'," +
      id +
      "," +
      columna +
      ')" ' +
      'style="display:none;width:130px;white-space: initial;cursor:pointer;word-break:break-word;">' +
      texto +
      "</span>" +
      "</div>"
    );
  }

  //Tipo numérico
  if (tipo == 3) {
    return (
      "<div>" +
      '<span style="display:none">' +
      textCorto +
      "</span>" +
      "<span " +
      'id="texto_corto_global_' +
      tabla +
      "_" +
      columna +
      "_" +
      id +
      '" ' +
      'style="cursor:pointer;" ' +
      "onclick=\"verTextoCortoCompletoGlobal('" +
      tabla +
      "', " +
      id +
      ", " +
      columna +
      ')">' +
      textCorto +
      "</span>" +
      "<span " +
      'id="texto_completo_global_' +
      tabla +
      "_" +
      columna +
      "_" +
      id +
      '" ' +
      'class="texto_completo_global" ' +
      'tabla="' +
      tabla +
      '" ' +
      'id_texto="' +
      id +
      '" ' +
      'columna="' +
      columna +
      '" ' +
      "onclick=\"verTextoCortoCompletoGlobal('" +
      tabla +
      "'," +
      id +
      "," +
      columna +
      ')" ' +
      'style="display:none;width:130px;white-space: initial;cursor:pointer;word-break:break-word;">' +
      texto +
      "</span>" +
      "</div>"
    );
  }
}

//Fin validar longitud texto datatables

function puntos_decimales_global(number) {
  return new Intl.NumberFormat("de-DE").format(number);
}

function primera_letra_mayuscula_global(string) {
  var texto = string.toLowerCase();

  var arr = texto.split(" ");

  arr[0] = arr[0].charAt(0).toUpperCase() + arr[0].slice(1);

  texto = arr.join(" ");

  return texto;
}

function primeras_letras_mayusculas_global(string) {
  var texto = string.toLowerCase();

  var arr = texto.split(" ");

  for (var i = 0; i < arr.length; i++) {
    arr[i] = arr[i].charAt(0).toUpperCase() + arr[i].slice(1);
  }

  texto = arr.join(" ");

  return texto;
}

//Ordenar JSON ascendete
function ordernarJsonAscGlobal(data, key) {
  return data.sort(function (a, b) {
    return a[key].localeCompare(b[key]);
  });
}

function validaciones_fechas(fecha_inicio, fecha_fin) {
  fecha_inicio = new Date(fecha_inicio.split("-"));
  fecha_fin = new Date(fecha_fin.split("-"));

  // Validamos que las fechas no vengan vacias.
  if ($.trim(fecha_inicio) == "" && $.trim(fecha_fin) == "") {
    Notificacion("Las fecha de inicio y fecha fin son obligatorias", "warning");
    return false;
  }

  // Validamos que la fecha inicial no sea mayor a la final.
  if (fecha_inicio > fecha_fin) {
    Notificacion(
      "La fecha de inicio no puede ser mayor a la fecha final",
      "warning"
    );
    return false;
  }

  // Validamos que el rango de fechas no sobrepase los 31 días.
  if (
    Math.round(
      (fecha_fin.getTime() - fecha_inicio.getTime()) / (1000 * 60 * 60 * 24)
    ) > 31
  ) {
    Notificacion(
      "El rango máximo entre fechas debe ser de 31 días.",
      "warning"
    );
    return false;
  }
}

function DisabledArbolDCS(nameinput, regional, subregion, ruta, circuito) {
  setInterval(() => {
    var input = $("#" + nameinput).val();
    var selectregional = $("#" + regional).val();
    var selectsubregion = $("#" + subregion).val();
    var selectruta = $("#" + ruta).val();
    var selectcircuito = $("#" + circuito).val();
    if (typeof input == "undefined") {
      $("#" + regional).attr("disabled", false);
      $("#" + subregion).attr("disabled", false);
      $("#" + ruta).attr("disabled", false);
      $("#" + circuito).attr("disabled", false);
      return false;
    }
    if (input.length > 0) {
      $("#" + regional).attr("disabled", true);
      $("#" + subregion).attr("disabled", true);
      $("#" + ruta).attr("disabled", true);
      $("#" + circuito).attr("disabled", true);

      $("#" + subregion).val("");
      $("#" + ruta).val("");
      $("#" + circuito).val("");
    } else {
      $("#" + regional).attr("disabled", false);
      $("#" + subregion).attr("disabled", false);
      $("#" + ruta).attr("disabled", false);
      $("#" + circuito).attr("disabled", false);
    }

    if (
      selectregional !== "" ||
      selectsubregion !== "" ||
      selectruta !== "" ||
      selectcircuito !== ""
    ) {
      $("#" + nameinput).attr("disabled", true);
    } else {
      $("#" + nameinput).attr("disabled", false);
    }
  }, 100);
}

function longitudTextoGlobal(
  tabla = null,
  id = null,
  texto = null,
  columna = null,
  tipo = null
) {
  //Tipo 1 = Datatable principal
  //Tipo 2 = Datatable modal
  if (tipo == 1) {
    limite = 23;
  }

  if (tipo == 2) {
    limite = 15;
  }

  //Numérico
  if (tipo == 3) {
    limite = 15;
  }

  if (id == null || columna == null) {
    $(".texto_completo_global").each(function () {
      var id = $(this).attr("id_texto");
      var columna = $(this).attr("columna");
      var tabla = $(this).attr("tabla");
      $("#texto_corto_global_" + tabla + "_" + columna + "_" + id).show();
      $("#texto_completo_global_" + tabla + "_" + columna + "_" + id).hide();
      estado_texto_global = false;
    });

    return;
  }

  //Texto
  if ((tipo == 1 || tipo == 2) && texto.length <= limite) {
    return (
      '<div><span style="display:none">' +
      texto +
      "</span><span>" +
      texto +
      "</span></div>"
    );
  }

  //Numérico
  if (tipo == 3 && texto.length <= limite) {
    return "$" + texto;
  }

  var textCorto = texto.substring(0, limite) + "...";

  //Tipo texto
  if (tipo == 1 || tipo == 2) {
    return (
      "<div>" +
      '<span style="display:none">' +
      textCorto +
      "</span>" +
      "<span " +
      'id="texto_corto_global_' +
      tabla +
      "_" +
      columna +
      "_" +
      id +
      '" ' +
      'style="cursor:pointer;" ' +
      "onclick=\"verTextoCortoCompletoGlobal('" +
      tabla +
      "'," +
      id +
      "," +
      columna +
      ')">' +
      textCorto +
      "</span>" +
      "<span " +
      'id="texto_completo_global_' +
      tabla +
      "_" +
      columna +
      "_" +
      id +
      '" ' +
      'class="texto_completo_global" ' +
      'tabla="' +
      tabla +
      '" ' +
      'id_texto="' +
      id +
      '" ' +
      'columna="' +
      columna +
      '" ' +
      "onclick=\"verTextoCortoCompletoGlobal('" +
      tabla +
      "'," +
      id +
      "," +
      columna +
      ')" ' +
      'style="display:none;width:130px;white-space: initial;cursor:pointer;word-break:break-word;">' +
      texto +
      "</span>" +
      "</div>"
    );
  }

  //Tipo numérico
  if (tipo == 3) {
    return (
      "<div>" +
      '<span style="display:none">' +
      textCorto +
      "</span>" +
      "<span " +
      'id="texto_corto_global_' +
      tabla +
      "_" +
      columna +
      "_" +
      id +
      '" ' +
      'style="cursor:pointer;" ' +
      "onclick=\"verTextoCortoCompletoGlobal('" +
      tabla +
      "', " +
      id +
      ", " +
      columna +
      ')">' +
      textCorto +
      "</span>" +
      "<span " +
      'id="texto_completo_global_' +
      tabla +
      "_" +
      columna +
      "_" +
      id +
      '" ' +
      'class="texto_completo_global" ' +
      'tabla="' +
      tabla +
      '" ' +
      'id_texto="' +
      id +
      '" ' +
      'columna="' +
      columna +
      '" ' +
      "onclick=\"verTextoCortoCompletoGlobal('" +
      tabla +
      "'," +
      id +
      "," +
      columna +
      ')" ' +
      'style="display:none;width:130px;white-space: initial;cursor:pointer;word-break:break-word;">' +
      texto +
      "</span>" +
      "</div>"
    );
  }
}

//Fin Form to Json



function formatBytes(bytes, decimals = 2) {
  if (!+bytes) return "0 Bytes";

  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;

  // Se cambia el formato de MiB a MB por conveniencia
  const sizes = ["Bytes", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}

//#region peticion http
function esJSON(item) {
  try {
    JSON.stringify(JSON.parse(item));
    return true;
  } catch (e) {
    return false;
  }
}

function objectEntries(obj) {
  let ownProps = Object.keys(obj),
    i = ownProps.length,
    resArray = new Array(i); // preallocate the Array
  while (i--) resArray[i] = [ownProps[i], obj[ownProps[i]]];

  return resArray;
}

function objectToFormData(obj, formData = new FormData(), namespace) {
  objectEntries(obj).forEach(([key, value]) => {
    const formKey = namespace ? `${namespace}[${key}]` : key;
    if (typeof obj[key] === "object" && !(obj[key] instanceof File)) {
      objectToFormData(obj[key], formData, formKey);
    } else formData.append(formKey, value);
  });

  return formData;
}

/**
* @param {string} modulo url del modulo
*/
function HttpRequest(modulo) {
  /** @private */
  this._baseUrl = modulo;

  this.url = `${this._baseUrl}/controlador.php`;

  /** @private */
  this._headers = {};

  /**
  * @private
  * @type {Record<string, File | Blob> | null}
  */
  this._sent_files = null;

  /** @private */
  this._body = {
    accion: "",
  };

  /** @private */
  this._method = "POST";

  /**
  * @param {string} url
  * @param {boolean} replace
  */
  this.setUrl = function (url, replace = false) {
    if (replace) {
      this.url = url;
    }

    this.url = `${this._baseUrl}/${url}`;
  };

  /**
  * @param {HeadersInit} headers
  */
  this.setHeaders = function (headers) {
    this._headers = Object.assign({}, this._headers, headers);
  };

  /**
  * Envia un archivo por formulario
  * @param {string} key_name
  * @param {File | Blob} file
  */
  this.appendFile = function (key_name, file) {
    if (!this._sent_files) this._sent_files = {};

    this._sent_files[key_name] = file;
  };

  /**
  * @param {BodyInit} body
  */
  this.setBody = function (body) {
    this._body = Object.assign({}, this._body, body);
  };

  /**
  * @param {string} action
  */
  this.setAction = function (action) {
    this._body.accion = action;
  };

  /**
  * @param {string} method
  */
  this.setMethod = function (method) {
    this._method = method.toUpperCase();
  };

  this.getHeaders = function () {
    return this._headers;
  };

  /** @private */
  this.appendFilesToFormData = function (formData) {
    if (!this._sent_files) return;

    const files = Object.entries(this._sent_files);

    while (files.length) {
      const file = files.pop();
      const key = file[0];
      const value = file[1];

      formData.append(key, value);
    }
  };

  /**
  * @returns {Promise<any>} response
  */
  this.getRequest = function () {
    const method = this._method;
    const url = this.url;
    const headers = this._headers;
    let body = { ...this._body };
    const appendFilesToFormData = this.appendFilesToFormData.bind(this);
    const files = this._sent_files;

    return new Promise(function (resolve, reject) {
      let xhr = new XMLHttpRequest();
      xhr.open(method, url, true);

      if (headers) {
        Object.keys(headers).forEach(function (key) {
          xhr.setRequestHeader(key, headers[key]);
        });
      }

      xhr.onload = function () {
        let response = xhr.response;
        const content_type = xhr.getResponseHeader("Content-Type");
        if (content_type === "application/json" || esJSON(response))
          response = JSON.parse(response);

        if (
          xhr.status < 200 ||
          xhr.status > 299 ||
          response?.error !== undefined
        )
          reject(response);
        else resolve(response);
      };

      xhr.onerror = function () {
        reject(xhr.response);
      };

      const formatted_body = objectEntries(body).filter(function (option) {
        const isArrayEmpty = Array.isArray(option[1]) && option[1].length;
        const isObjectEmpty =
          typeof option[1] === "object" && Object.keys(option[1]).length;
        const isOther =
          typeof option[1] !== "object" && !Array.isArray(option[1]);
        return Boolean(
          option[1] !== undefined &&
          option[1] !== null &&
          (isArrayEmpty || isObjectEmpty || isOther),
        );
      });

      body = Object.fromEntries(formatted_body);

      body = objectToFormData(body);

      // Agregamos los archivos si existen
      appendFilesToFormData(body);

      let payload = body;

      // Si no hay archivos, enviamos el body como un string
      if (files === null) {
        payload = new URLSearchParams(body).toString();
        // Cambiamos el tipo de contenido a application/x-www-form-urlencoded
        xhr.setRequestHeader(
          "Content-Type",
          "application/x-www-form-urlencoded",
        );
      }

      xhr.send(payload);
    });
  };
}
//#endregion

//#region ImageDropzone
/**
* @param {HTMLElement} target
* @param {Function} onEdit
*/
function ImageDropzone(target, onEdit) {
  /** @type {HTMLElement} */
  this.target = target;

  /** @type {number} */
  this.max_size = 1024 * 1024 * 1;
  /** @type {number} */
  this.min_size = 0;
  /** @type {number} */
  this.max_images = 1;
  /** @type {number} */
  this.min_images = 1;

  /** @type {number | null} */
  this.max_image_length = null;

  /** @type {boolean} */
  this.can_edit = true;

  /** @type {boolean} */
  this.is_dragging = false;

  /** @type {HTMLImageElement[]} */
  this.images = [];

  /** @type {FileList | File[] | null} */
  this.files = [];

  /** @type {HTMLElement | null} */
  this.image_preview = null;

  /** @type {HTMLInputElement | null} */
  this.input_file = null;

  /** @type {Array<string>} */
  this.valid_mime_types = [
    "image/png",
    "image/jpg",
    "image/jpeg",
    "image/svg+xml",
  ];

  /** @type {HTMLDivElement | null} */
  this.container = null;

  /** @type {HTMLDivElement | null} */
  this.image_container = null;

  /** @type {Function | null} */
  this.onEdit = null;

  /** @type {Function | null} */
  this.onEdit = null;

  /** @type {Function | null} */
  this.onChange = null;

  /** @type {Function | null} */
  this.onRemove = null;

  /** @private */
  this.removeImage = function () {
    if (!this.images.length || !this.image_preview) return;

    // Borramos la primera imagen
    // TODO: eliminar imagen dependiendo del indice proporcionado
    this.images.splice(0, 1);

    // Eliminamos la imagen previsualizada
    this.image_preview.remove();

    // Reiniciamos las variables
    this.image_preview = null;

    // Eliminamos la clase "preview" del elemento contenedor
    this.container.classList.remove("preview");

    if (!this.onRemove) return;

    this.onRemove(0);
  };

  /**
  * @private
  */
  this.editImage = function () {
    if (!this.onEdit) return;

    const image_preview = this.image_preview.cloneNode(true);

    this.onEdit(image_preview);
  };

  /**
  * @private
  * @param {HTMLElement} container
  * @param {HTMLInputElement} input
  */
  this.handleClick = function (container, input) {
    // Se guarda la instancia de este para poder acceder a los atributos
    const _this = this;
    container.addEventListener("click", function (event) {
      if (_this.image_preview) return event.preventDefault();
      input.click();
    });
  };

  /** @private */
  this.renderImages = function () {
    if (!this.images.length) return;

    this.images[0].classList.add("image-preview", "loading");

    this.image_preview = this.images[0];

    if (!this.container) return;

    const image_preview = this.image_preview;

    this.image_preview.addEventListener("load", function () {
      image_preview.classList.remove("loading");
    });

    this.image_preview.addEventListener("error", function () {
      image_preview.classList.remove("loading");
    });

    this.container.classList.add("preview");

    this.image_container.innerHTML = "";

    this.image_container.append(this.image_preview);
  };

  /** @private */
  this.validateFile = function (file) {
    const mime_type = file.type;
    const valid_mime_types = this.valid_mime_types;
    const min_size = this.min_size;
    const max_size = this.max_size;

    // Validamos que el tipo de archivo sea válido
    if (!valid_mime_types.includes(mime_type)) {
      const message = `El archivo no es válido. Permite: ${this.formatValidMimeTypes()}`;
      Notificacion(message, "error");

      throw new Error(message);
    }

    // Validamos que el tamaño maximo del archivo
    if (min_size && file.size < min_size) {
      const message = `El peso del archivo no puede ser menor a ${formatBytes(min_size)}`;
      Notificacion(message, "error");

      throw new Error(message);
    }

    // Validamos que el tamaño minimo del archivo
    if (max_size && file.size > max_size) {
      const message = `El peso del archivo no puede ser mayor a ${formatBytes(max_size)}`;
      Notificacion(message, "error");

      throw new Error(message);
    }

    return true;
  };

  /**
  * @private
  * @param {File} file
  */
  this.readFile = function (file) {
    return new Promise(function (resolve, reject) {
      const reader = new FileReader();
      reader.onload = function (event) {
        // Creamos una imagen nueva para poder manipularla
        const image = new Image();
        // Se establece el contenido de la imagen
        image.src = event.target.result;

        resolve(image);
      };
      reader.readAsDataURL(file);
    });
  };

  /**
  * @private
  * @param {HTMLImageElement} image
  * @param {number} limit
  */
  this.reduceImageLength = function (image, limit) {
    const canvas = document.createElement("canvas");
    const image_copy = image.cloneNode(true);
    const ctx = canvas.getContext("2d");
    let reducedWidth = image.width;
    let reducedHeight = image.height;
    document.body.appendChild(canvas);
    canvas.appendChild(image_copy);

    // Si el alto de la imagen es mayor que el largo máximo permitido, se ajusta el ancho
    if (image_copy.naturalHeight > image_copy.naturalWidth) {
      reducedHeight = limit;
      reducedWidth =
        image_copy.naturalWidth * (limit / image_copy.naturalHeight);
      // Validamos si el ancho de la imagen es mayor que el largo máximo permitido
    } else if (image_copy.naturalWidth > image_copy.naturalHeight) {
      reducedWidth = limit;
      reducedHeight =
        image_copy.naturalHeight * (limit / image_copy.naturalWidth);
      // Si ambos son iguales, se limita a la longitud máxima
    } else {
      reducedWidth = limit;
      reducedHeight = limit;
    }

    // Se crea el canvas y se dibuja la imagen reducida
    canvas.width = reducedWidth;
    canvas.height = reducedHeight;
    ctx.drawImage(image_copy, 0, 0, reducedWidth, reducedHeight);

    const dataURL = canvas.toDataURL("image/png", 1);

    canvas.remove();

    // Se crea una nueva imagen para poder manipularla
    const new_image = new Image();
    new_image.src = dataURL;

    return new_image;
  };

  /**
  * @private
  * @param {FileList} files
  */
  this.transformImages = function (files) {
    // Se guarda la instancia de este para poder acceder a los atributos
    const _this = this;
    const result = [];

    return new Promise(function (resolve, reject) {
      let index = 0;

      // Funcion recursiva para leer cada archivo
      function readNextFile() {
        if (index >= files.length) {
          resolve(result);
          return;
        }

        const file = files[index];
        index++;

        try {
          _this.validateFile(file);

          // Se transforma el archivo, si alguno no fue transformado exitosamente, se imprime el error en consola y se carga el siguiente
          _this
            .readFile(file)
            .then(function (image) {
              // Validamos si las dimensiones de la imagen son mayores que el máximo permitido
              const is_limit_surppassed =
                image.width > _this.max_image_length ||
                image.height > _this.max_image_length;
              if (_this.max_image_length && is_limit_surppassed)
                image = _this.reduceImageLength(image, _this.max_image_length);
              result.push(image);
              readNextFile();
            })
            .catch(function (error) {
              console.error(error);
              readNextFile();
            });
        } catch (error) {
          reject(error);
        }
      }

      // Leemos el siguiente archivo
      readNextFile();
    });
  };

  /**
  * @private
  * @param {HTMLElement} container
  */
  this.handleDragAndDrop = function (container) {
    // Se guarda la instancia de este para poder acceder a los atributos
    const _this = this;

    container.addEventListener("dragover", function (event) {
      event.preventDefault();
    });

    container.addEventListener("dragleave", function (event) {
      event.preventDefault();
    });

    container.addEventListener("drop", function (event) {
      event.preventDefault();

      const files = event.dataTransfer.files;
      _this.transformImages(files).then(function (result) {
        _this.images = result;
        _this.files = files;

        if (_this.onChange) _this.onChange(result, files);

        _this.renderImages();
        _this.input_file.value = "";
      });
    });
  };

  /** @private */
  this.buildReader = function () {
    const input_file = document.createElement("input");
    input_file.type = "file";
    input_file.multiple = this.max_images > 1;
    input_file.accept = this.valid_mime_types.join(",");

    this.input_file = input_file;

    const _this = this;

    this.input_file.onclick = function () {
      this.value = null;
    }

    this.input_file.addEventListener("change", function (event) {
      const files = event.target.files;
      _this.transformImages(files).then(function (result) {
        _this.images = result;
        _this.files = files;

        if (_this.onChange) _this.onChange(result, files);

        _this.renderImages();
        _this.input_file.value = "";
      });
    });

    return input_file;
  };

  /** @private */
  this.formatValidMimeTypes = function () {
    const valid_mime_types = this.valid_mime_types;
    let formatted_valid_mime_types = "";

    valid_mime_types.forEach(function (mime_type) {
      const formatted_mime_type = mime_type.split("/")[1];
      formatted_valid_mime_types += formatted_mime_type + ", ";
    });

    return formatted_valid_mime_types.slice(0, -2);
  };

  /** @private */
  this.buildTextInfo = function () {
    const container = document.createElement("div");
    container.classList.add("info");

    const title = document.createElement("span");
    title.innerText = "Suelte los archivos aquí o haga clic para cargar";

    const formatted_valid_mime_types = this.formatValidMimeTypes();

    const valid_types = document.createElement("span");
    valid_types.innerText = "Permite: " + formatted_valid_mime_types;

    container.append(title, valid_types);

    if (this.min_size) {
      const min_size = document.createElement("span");
      min_size.innerText = "Tamaño mínimo: " + formatBytes(this.min_size);
      container.append(min_size);
    }

    if (this.max_size) {
      const max_size = document.createElement("span");
      max_size.innerText = "Tamaño máximo: " + formatBytes(this.max_size);
      container.append(max_size);
    }

    return container;
  };

  /** @private */
  this.buildContainer = function () {
    const container = document.createElement("div");
    container.classList.add("image-dropzone-viewer");

    return container;
  };

  /** @private */
  this.handlePreviewActionsListeners = function (edit_action, delete_action) {
    const _this = this;

    edit_action.addEventListener("click", function (event) {
      _this.editImage();
    });

    delete_action.addEventListener("click", function (event) {
      event.stopPropagation();
      _this.removeImage();
    });
  };

  /** @private */
  this.buildPreviewActions = function () {
    const container = document.createElement("div");
    container.classList.add("preview-actions");

    if (!this.can_edit) {
      container.innerHTML = "";
      return container;
    }

    // Boton de editar
    const edit_action = document.createElement("button");
    edit_action.type = "button";
    edit_action.classList.add("preview-action");

    const edit_text = document.createTextNode("Editar");

    // Icono de editar
    const edit_icon = document.createElement("i");
    edit_icon.classList.add("glyphicon", "glyphicon-pencil");

    edit_action.append(edit_icon, edit_text);

    // Boton de eliminar
    const delete_action = document.createElement("button");
    delete_action.type = "button";
    delete_action.classList.add("preview-action");

    // Icono de eliminar
    const delete_icon = document.createElement("i");
    delete_icon.classList.add("glyphicon", "glyphicon-trash");

    delete_action.append(delete_icon);

    this.handlePreviewActionsListeners(edit_action, delete_action);

    container.append(edit_action, delete_action);

    return container;
  };

  /** @private */
  this.buildEmptyState = function () {
    const image = document.createElement("img");
    image.src = "static/img/image-dropzone-viewer/bg.svg";
    image.classList.add("empty-state");

    return image;
  };

  /** @private */
  this.buildImageContainer = function () {
    const container = document.createElement("div");
    container.classList.add("image-container");

    return container;
  };

  /**
  * @param {number} max_image_length
  */
  this.setMaxImageLength = function (max_image_length) {
    this.max_image_length = max_image_length;
  };

  this.setCanEdit = function (can_edit) {
    this.can_edit = can_edit;
  };

  /**
  * @param {string[]} valid_mime_types
  */
  this.setValidMimeTypes = function (valid_mime_types) {
    this.valid_mime_types = valid_mime_types;
  };

  /**
  * @param {number} max_images
  */
  this.setMaxImages = function (max_images) {
    if (this.input_file) this.input_file.multiple = max_images > 1;

    this.max_images = max_images;
  };

  /**
  * @param {number} min_size
  */
  this.setMinSize = function (min_size) {
    this.min_size = min_size;
  };

  /**
  * @param {number} max_size
  */
  this.setMaxSize = function (max_size) {
    this.max_size = max_size;
  };

  this.setImages = function (images) {
    if (!images.length) return;
    this.images = images;

    // Se renderiza la imagen si existe
    this.renderImages();
  };

  this.build = function () {
    if (!this.target) throw new Error("Target no establecido");

    // Construimos el contenedor y el input
    const container = this.buildContainer();
    const input_file = this.buildReader();
    const empty_state = this.buildEmptyState();
    const preview_actions = this.buildPreviewActions();

    const text_info = this.buildTextInfo();

    const image_container = this.buildImageContainer();

    // Agregamos el input al contenedor
    container.append(
      input_file,
      empty_state,
      text_info,
      preview_actions,
      image_container,
    );

    // Agregamos el evento de arrastrar y soltar
    this.handleDragAndDrop(container);

    this.handleClick(container, input_file);

    this.target.appendChild(container);

    this.container = container;

    this.image_container = image_container;

    return container;
  };

  this.destroy = function () {
    if (this.container) this.container.remove();
    this.container = null;
    this.image_container = null;
    this.image_preview = null;
  };
}
//#endregion


// inicio de configuracion para el codigo personalizado
function global_configuracion_general(columnInput) {
  fetch("funciones/controlador.php", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify({
      accion: "configuracion_general"
    })
  }).then(response => response.json()).then(data => {
    if (data.estado_codigo_territorio == 1) {
      configuracion_codigo_personalizado(data, columnInput)
    }
  }).catch(error => {
    console.error('Error:', error);
  });
}

var global_codigo_personalizado = 0
var global_nombre_personalizado = ''
var global_longitud_personalizado = 0
function configuracion_codigo_personalizado(datos, columnInput) {
  columnInput = columnInput ? columnInput : 'col-md-4'
  global_codigo_personalizado = datos.estado_codigo_territorio
  global_nombre_personalizado = datos.codigo_territorio
  global_longitud_personalizado = datos.longitud_codigo_territorio
  if ($(".global_nombre_personalizado")[0]) {
    $(".global_nombre_personalizado").text(global_nombre_personalizado.toUpperCase())
    $(".global_nombre_personalizado")[0].classList.remove("hidden")
  }
  let content_filtro_codigo_personalizado = document.getElementsByClassName('content_filtro_codigo_personalizado')[0]
  if (content_filtro_codigo_personalizado) {
    content_filtro_codigo_personalizado.className = 'form-group ' + columnInput
    content_filtro_codigo_personalizado.innerHTML = `<label for="name_personalizado">${global_nombre_personalizado}:</label>
                                                      <input type="text" name="filtro_codigo_personalizado" maxlength="${global_longitud_personalizado}" id="filtro_codigo_personalizado" class="form-control">`
  }

  let content_input_codigo_personalizado = document.getElementsByClassName('content_input_codigo_personalizado')[0]
  if (content_input_codigo_personalizado) {
    content_input_codigo_personalizado.className = 'col-md-6'
    content_input_codigo_personalizado.innerHTML = `<div class="form-group">
                                                    <label>${global_nombre_personalizado}:</label>
                                                    <input type="text" name="input_codigo_personalizado" maxlength="${global_longitud_personalizado}" pattern="[A-Za-z0-9 ]+" id="input_codigo_personalizado" class="form-control">
                                                  </div>`
  }
}

function globalToggleSelect(input) {
  const selectPersonalizado = document.getElementsByClassName('dcs_personalizado')
  if (selectPersonalizado.length > 0) {
    $(selectPersonalizado[0]).val("").change()
    if (input.value.trim() !== '') {
      for (const row of selectPersonalizado) {
        row.disabled = true
      }
      // Deshabilita el select
    } else {
      for (const row of selectPersonalizado) {
        row.disabled = false
      }
      // Habilita el select
    }
  }

}

// fin de configuracion para el codigo personalizado


/**
* Muestra el loader
*/
function showLoader() {
  if (!loader) {
    loader = $("body")
      .loadingIndicator({ useImage: !1 })
      .data("loadingIndicator");
  }
  loader.show();
}

/**
 * Oculta el loader
 */
function hideLoader() {
  if (loader) {
    loader.hide();
  };
}

/**
* Controla el loader de acuerdo a si hay o no peticiones en ejecucion
* 
* @param {string} key llave para identificar que hay una carga en ejecucion
*/
function isLoading(key = false) {
  (key) ? _loading[key] = (_loading[key] ? !_loading[key] : true) : false;
  let isLoading = Object.keys(_loading).filter((i) => _loading[i]);
  (isLoading.length > 0) ? showLoader() : hideLoader();
  return isLoading.length > 0;
}

/**
 * Formatea el numero de acuerdo al pais
 * @param {Int} number 
 * @returns {String}
 */
function numberFormat(number) {
  return new Intl.NumberFormat("es-CO").format(number)
}

/**
 * Carga contenido HTML de una URL y lo inserta en un elemento del DOM.
 * @param {string} selector El selector CSS del elemento donde se insertará el HTML.
 * @param {string} url La URL del archivo HTML a cargar.
 */
function loadHtml(selector, url) {
  const targetElement = document.querySelector(selector);
  if (!targetElement) {
    return;
  }
  fetch(url).then(response => {
    if (!response.ok) {
      // Si la respuesta no es OK (ej. 404 Not Found, 500 Internal Server Error)
      throw new Error(`Error HTTP: ${response.status} - ${response.statusText}`);
    }
    return response.text(); // Obtiene el contenido de la respuesta como texto
  }).then(html => {
    targetElement.innerHTML = html; // Inserta el HTML en el elemento
    // console.log(`Contenido de ${url} cargado en ${selector}`);
  }).catch(error => {
    console.error(`Error al cargar ${url}:`, error);
    targetElement.innerHTML = `<p style="color: red;">Error al cargar el contenido: ${error.message}</p>`;
  });
}